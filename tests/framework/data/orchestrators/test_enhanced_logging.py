#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for enhanced logging and error handling in orchestrators.

This script demonstrates the new comprehensive logging, progress tracking,
execution time monitoring, and error handling functionality.
"""

import json
import sys
import os
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../../framework/data', '..', '..'))

from framework.utils.common import CommonUtils
from framework.data.orchestrators.utils.orchestrator_monitoring import OrchestratorMonitor


def test_orchestrator_monitor():
    """Test the OrchestratorMonitor functionality."""
    
    print("Testing OrchestratorMonitor functionality...")
    
    # Initialize CommonUtils
    common_utils = CommonUtils(
        name="test_orchestrator_monitor",
        log_level="INFO"
    )
    
    # Initialize monitor
    monitor = OrchestratorMonitor(common_utils, "TestOrchestrator")
    
    # Test global execution tracking
    monitor.start_global_execution()
    
    # Test progress reporting
    monitor.report_progress("binance", "btc_usdt", "tick_data_writer", 0.0)
    monitor.report_progress("binance", "btc_usdt", "tick_data_writer", 0.5)
    monitor.report_progress("binance", "btc_usdt", "tick_data_writer", 1.0)
    
    # Test execution time tracking with resource monitoring
    try:
        with monitor.track_execution_time("test_writer", "binance", "btc_usdt") as metrics:
            # Simulate some work that uses memory
            import time
            # Create some data to use memory
            large_data = [i * 2 for i in range(100000)]  # Use some memory
            time.sleep(1)
            # Process the data
            processed_data = sum(large_data)
            common_utils.log.info(f"Simulated work completed successfully, processed {len(large_data)} items, sum: {processed_data}")
            del large_data, processed_data  # Clean up
    except Exception as e:
        common_utils.log.error(f"Error in execution time tracking test: {e}")
    
    # Test error handling
    try:
        with monitor.track_execution_time("error_writer", "binance", "eth_usdt") as metrics:
            # Simulate an error
            raise ValueError("Simulated error for testing")
    except Exception as e:
        # This should be handled by the monitor
        pass
    
    # Test another successful operation with different memory pattern
    try:
        with monitor.track_execution_time("volume_data_writer", "coinbase", "btc_usd") as metrics:
            import time
            # Simulate different memory usage pattern
            data_chunks = []
            for i in range(10):
                chunk = [j for j in range(10000)]  # Create smaller chunks
                data_chunks.append(chunk)
                time.sleep(0.05)  # Simulate processing time

            # Process all chunks
            total_items = sum(len(chunk) for chunk in data_chunks)
            common_utils.log.info(f"Another operation completed successfully, processed {total_items} items in {len(data_chunks)} chunks")
            del data_chunks  # Clean up
    except Exception as e:
        common_utils.log.error(f"Error in second operation: {e}")
    
    # End global execution and generate summary
    monitor.end_global_execution()
    summary = monitor.generate_execution_summary()
    
    # Print summary as JSON for inspection
    print("\n" + "="*80)
    print("EXECUTION SUMMARY (JSON)")
    print("="*80)
    print(json.dumps(summary, indent=2))
    
    # Finalize CommonUtils
    common_utils.finalize()
    
    print("\nTest completed successfully!")


def test_error_handling():
    """Test error handling functionality."""
    
    print("\nTesting error handling functionality...")
    
    # Initialize CommonUtils
    common_utils = CommonUtils(
        name="test_error_handling",
        log_level="INFO"
    )
    
    # Initialize monitor
    monitor = OrchestratorMonitor(common_utils, "ErrorTestOrchestrator")
    
    # Test different types of errors
    test_errors = [
        ImportError("Module not found"),
        ConnectionError("Connection failed"),
        ValueError("Invalid value"),
        RuntimeError("Runtime error"),
        KeyError("Key not found")
    ]
    
    for i, error in enumerate(test_errors):
        writer_type = f"test_writer_{i}"
        exchange = "test_exchange"
        pair = f"test_pair_{i}"
        
        can_continue = monitor.handle_writer_error(error, writer_type, exchange, pair)
        common_utils.log.info(f"Error {type(error).__name__} - Can continue: {can_continue}")
    
    # Generate summary
    summary = monitor.generate_execution_summary()
    
    print(f"\nTotal errors handled: {len(summary['errors'])}")
    for error in summary['errors']:
        print(f"- {error['error_type']}: {error['error_message']}")
    
    # Finalize CommonUtils
    common_utils.finalize()
    
    print("Error handling test completed!")


if __name__ == "__main__":
    print("Starting enhanced logging and error handling tests...")
    print("="*80)
    
    try:
        test_orchestrator_monitor()
        test_error_handling()
        
        print("\n" + "="*80)
        print("ALL TESTS COMPLETED SUCCESSFULLY!")
        print("="*80)
        
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
