# PBI-1: Spot and Futures Data Orchestrators

## Overview

Create two separate orchestrator scripts (`spot_pair_data_orchestrator.py` and `futures_pair_data_orchestrator.py`) that automate the complete backfill process for trading pairs across all supported datasets. These orchestrators will eliminate the manual, error-prone process of running individual writers by providing configuration-driven execution with proper dependency management.

## Problem Statement

Currently, onboarding new trading pairs for data collection requires:
1. Manually running each data writer separately (tick_data_writer, price_derived_data_writer, etc.)
2. Ensuring proper execution order based on data dependencies
3. Managing different configurations for each writer type
4. Coordinating between spot and futures data with different performance characteristics
5. Handling different instrument naming conventions (spot: `btc_usdt`, futures: `BTCUSDT`)

This manual process is time-consuming, error-prone, and doesn't scale well as we add more assets and data types.

## User Stories

**Primary User Story:**
As a data operations team member, I want separate orchestrators for spot and futures data backfill so that I can efficiently onboard new trading pairs across all supported datasets without manual writer coordination.

**Supporting User Stories:**
- As a data engineer, I want a simple configuration structure so that I can easily specify which pairs to backfill from which exchanges
- As a system operator, I want automatic dependency management so that data writers execute in the correct order without manual intervention
- As a data analyst, I want separate spot and futures orchestrators so that I can run fast futures backfills without waiting for slow spot data processing

## Technical Approach

### Architecture Overview

**Two Separate Orchestrators:**
1. **`spot_pair_data_orchestrator.py`** - Handles spot market data with complex dependencies
2. **`futures_pair_data_orchestrator.py`** - Handles futures market data with simpler, mostly independent data types

### Configuration Structure

**Spot Orchestrator Configuration:**
```python
SPOT_PAIR_CONFIG = {
    "start_date": "2021-01-01-00:00:00",
    "end_date": "2025-01-20-00:00:00",
    "parallel": True,
    "pairs": {
        "binance": ["hbar_usdt", "hbar_usdc"],
        "bybit": ["hbar_usdt"],
        "coinbase": ["hbar_usd", "hbar_usdt"],
        "okex": ["hbar_usdt", "hbar_usdc"],
        "huobi": ["hbar_usdt"],
        "bitget": ["hbar_usdt"],
        "mexc": ["hbar_usdt", "hbar_usdc"]
    }
}
```

**Futures Orchestrator Configuration:**
```python
FUTURES_PAIR_CONFIG = {
    "start_date": "2021-01-01-00:00:00",
    "end_date": "2025-01-20-00:00:00",
    "parallel": True,
    "pairs": {
        "binance": ["HBARUSDT", "HBARUSDC"],
        "bybit": ["HBARUSDT"],
        "bitmex": ["HBARUSD"]
    }
}
```

### Dependency Management

**Spot Data Dependencies:**
1. **Level 1 (Raw Data):** `tick_data_writer`
2. **Level 2 (Derived from Raw):** `price_derived_data_writer`, `volume_data_writer`, `orderbook_data_writer`
3. **Level 3 (Derived from Level 2):** `volatility_data_writer`
4. **Level 4 (Cross-instrument):** `correlation_data_writer`
   ** Level 4.1**: `correlation_data_writer -l 1h`
   ** Level 4.2**: `correlation_data_writer -l 12h`
   ** Level 4.3**: `correlation_data_writer -l 1d`
5. **Level 5 (Market-wide):** `price_market_index_data_writer`

**Futures Data (Mostly Independent):**
- `funding_data_writer`
- `liquidations_data_writer`
- `open_interest_data_writer`
- `long_short_data_writer`

### Writer Integration

Each orchestrator will:
1. Generate writer-specific configurations from the pair configuration
2. Execute writers in dependency order
3. Use the same argument patterns as `generic_data_generator.py`
4. Handle writer-specific argument formatting (following existing patterns)
5. Provide comprehensive logging and error reporting
6. Implement checkpointing after each writer completion for failure recovery

## UX/UI Considerations

**Configuration Management:**
- Simple, human-readable configuration structures
- Clear separation between spot and futures configurations
- Easy to add new exchange/pair combinations
- Flexible date range specification

**Execution Feedback:**
- Progress reporting for long-running operations
- Clear dependency execution order logging
- Detailed error reporting with context
- Success/failure status for each writer

**Operational Efficiency:**
- Independent execution of spot vs futures orchestrators
- Parallel processing support where appropriate
- Resource-aware execution for large date ranges

## Acceptance Criteria

### Functional Requirements

1. **Spot Orchestrator:**
   - ✅ Executes all spot data writers in correct dependency order
   - ✅ Handles user-specified exchange/pair combinations
   - ✅ Supports tick, price_derived, volume, orderbook, volatility, correlation, and market_index data
   - ✅ Matches output quality of individual writers

2. **Futures Orchestrator:**
   - ✅ Executes all futures data writers (funding, liquidations, open_interest, long_short)
   - ✅ Handles futures instrument naming conventions
   - ✅ Supports independent execution of futures data types
   - ✅ Provides fast execution for futures data backfills

3. **Configuration Management:**
   - ✅ Simple, maintainable configuration structure
   - ✅ Easy to add new exchange/pair combinations
   - ✅ Clear separation between spot and futures configurations
   - ✅ Flexible date range specification

4. **Failure Recovery & Checkpointing:**
   - ✅ Automatic checkpoint creation after each writer completion
   - ✅ Resume capability from last successful checkpoint on restart
   - ✅ State persistence across orchestrator crashes
   - ✅ Clear indication of incomplete vs completed work

5. **Modularity:**
   - ✅ Easy to add new writers with minimal code changes
   - ✅ Clear dependency management system
   - ✅ Extensible architecture for future data types
   - ✅ Well-documented integration points

### Performance Requirements

1. **Execution Efficiency:**
   - ✅ Efficient execution with parallel processing support
   - ✅ Resource management during intensive backfills
   - ✅ Matches or exceeds performance of manual execution

2. **Operational Reliability:**
   - ✅ Graceful error handling without stopping entire process
   - ✅ Progress reporting for long-running operations
   - ✅ Clear logging of execution status and errors

## Dependencies

### Technical Dependencies
- Existing writer modules in `framework/data/writers/`
- `generic_data_generator.py` patterns for writer argument formatting
- Database connectivity through existing RDS/DynamoDB managers
- Logging infrastructure through CommonUtils

### Related files

#### Spot Orchestrator 
1. **Level 1 (Raw Data):** `framework/data/writers/tick_data_writer.py`
2. **Level 2 (Derived from Raw):** `framework/data/writers/price_derived_data_writer.py`, `framework/data/writers/volume_data_writer.py`, `framework/data/writers/orderbook_data_writer.py`
3. **Level 3 (Derived from Level 2):** `framework/data/writers/volatility_data_writer.py`
4. **Level 4 (Cross-instrument):** `framework/data/writers/correlation_data_writer.py`
5. **Level 5 (Market-wide):** `framework/data/writers/price_market_index_data_writer.py`

#### Futures Orchestrator
1. `framework/data/writers/funding_data_writer.py`
2. `framework/data/writers/liquidations_data_writer.py`
3. `framework/data/writers/open_interest_data_writer.py`
4. `framework/data/writers/long_short_data_writer.py`


### Data Dependencies
- AmberData API access for raw data retrieval
- Existing database schema for all data types
- Exchange API connectivity for various data sources

## Open Questions

1. ~~**Configuration Validation:** Should we add validation to check if specified pairs are available on specified exchanges?~~ **RESOLVED:** No validation needed - user should know what they're doing.

2. ~~**Checkpointing Granularity:** Should checkpoints be created at writer level, exchange level, or pair level?~~ **RESOLVED:** Checkpoints at lowest level capturing full state:
   - Exchange: `binance`
   - Pair: `hbar_usdt` 
   - Current data set: `tick_data_writer`
   - Last confirmed date processed: `2024-06-15-23:59:59`
   - Orchestrator type: `spot` or `futures`
   - Job configuration hash: `abc123` (to detect config changes)

3. ~~**Checkpoint Storage:** Should checkpoints be stored in files, database, or Redis for persistence?~~ **RESOLVED:** Local JSON files for simplicity - no external dependencies.

4. ~~**Dry Run Mode:** Should we add a dry-run mode to preview configurations without execution?~~ **RESOLVED:** Not needed.

5. ~~**Performance Monitoring:** Should we add execution time tracking and performance metrics?~~ **RESOLVED:** Yes, add execution time tracking per writer. No additional performance metrics needed initially.

## Related Tasks

[View Task List](mdc:tasks.md)

## Implementation Status

### ✅ **COMPLETED** - December 26, 2025

**All tasks completed successfully:**
- ✅ Task 1-1: Create spot pair data orchestrator foundation
- ✅ Task 1-2: Implement Level 1 writer integration (Raw Data)
- ✅ Task 1-3: Implement Level 2 writer integration (Derived from Raw)
- ✅ Task 1-4: Implement Level 3 writer integration (Volatility)
- ✅ Task 1-5: Implement Level 4 writer integration (Cross-instrument)
- ✅ Task 1-6: Implement Level 5 writer integration (Market-wide)
- ✅ Task 1-7: Create futures pair data orchestrator foundation
- ✅ Task 1-8: Implement futures writer integration
- ✅ Task 1-9: Add comprehensive logging and error handling
- ✅ Task 1-10: Create integration tests and validation

**Key Deliverables:**
- Two production-ready orchestrators with comprehensive monitoring
- Configuration-driven execution with dependency management
- Checkpointing and recovery capabilities
- Parallel processing support
- Enhanced logging and error handling
- Comprehensive test coverage

**Status**: ✅ **PRODUCTION READY** - Ready for deployment and operational use