#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Base Orchestrator Class

This module provides a base orchestrator class that consolidates common functionality
using all the utility modules for checkpoint management, writer execution, parallel processing,
exchange handling, and configuration management.
"""

import os
from typing import Dict, List, Any, Optional
from abc import ABC, abstractmethod

from framework.utils.common import CommonUtils
from .orchestrator_monitoring import OrchestratorMonitor
from .checkpoint_manager import CheckpointManager
from .writer_executor import WriterExecutor, WriterExecutionConfig
from .parallel_executor import ParallelExecutor
from .exchange_handler import ExchangeHandler
from .config_manager import ConfigManager


class BaseOrchestrator(ABC):
    """
    Base class for data orchestrators with common functionality.
    
    This class provides shared functionality for checkpoint management,
    writer execution, parallel processing, exchange handling, and configuration
    management that can be used by both spot and futures orchestrators.
    """
    
    def __init__(self, config: Dict[str, Any], common_utils: CommonUtils, orchestrator_type: str):
        """
        Initialize the base orchestrator.
        
        Args:
            config: Configuration dictionary
            common_utils: CommonUtils instance for logging and utilities
            orchestrator_type: Type of orchestrator (spot or futures)
        """
        self.config = config
        self.common_utils = common_utils
        self.logger = common_utils.log
        self.orchestrator_type = orchestrator_type
        
        # Initialize utility managers
        self._initialize_utilities()
        
        # Validate and normalize configuration
        self._validate_configuration()
        
        # Set up checkpoint directory
        self.checkpoint_dir = self.config.get("checkpoint_dir", f"checkpoints/{orchestrator_type}")
        
        self.logger.info(f"Initialized {orchestrator_type} orchestrator with {len(self.config.get('pairs', {}))} exchanges")
    
    def _initialize_utilities(self) -> None:
        """Initialize all utility managers."""
        # Configuration manager
        self.config_manager = ConfigManager(self.common_utils)
        
        # Monitoring
        orchestrator_name = f"{self.orchestrator_type.title()}PairDataOrchestrator"
        self.monitor = OrchestratorMonitor(self.common_utils, orchestrator_name)
        
        # Checkpoint manager
        checkpoint_dir = self.config.get("checkpoint_dir", f"checkpoints/{self.orchestrator_type}")
        self.checkpoint_manager = CheckpointManager(self.common_utils, checkpoint_dir, self.config)
        
        # Writer executor
        self.writer_executor = WriterExecutor(self.common_utils, self.monitor, self.checkpoint_manager)
        
        # Parallel executor
        max_workers = self.config.get("max_workers", 10)
        self.parallel_executor = ParallelExecutor(self.common_utils, max_workers)
        
        # Exchange handler
        self.exchange_handler = ExchangeHandler(self.common_utils)
    
    def _validate_configuration(self) -> None:
        """Validate and normalize the configuration."""
        validation_result = self.config_manager.validate_config(self.config, self.orchestrator_type)
        
        if not validation_result.is_valid:
            error_msg = f"Configuration validation failed: {', '.join(validation_result.errors)}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)
        
        # Log warnings
        for warning in validation_result.warnings:
            self.logger.warning(warning)
        
        # Use normalized config
        if validation_result.normalized_config:
            self.config = validation_result.normalized_config
        
        # Validate pairs for each exchange
        for exchange, pairs in self.config["pairs"].items():
            if not isinstance(pairs, list):
                raise ValueError(f"Pairs for exchange {exchange} must be a list")
    
    def execute_writer_for_pair(self, writer_type: str, exchange: str, pair: str, 
                               start_date: str, end_date: str, **kwargs) -> bool:
        """
        Execute a writer for a specific exchange and pair.
        
        Args:
            writer_type: Type of writer to execute
            exchange: Exchange name
            pair: Trading pair
            start_date: Start date for execution
            end_date: End date for execution
            **kwargs: Additional arguments for writer execution
            
        Returns:
            True if execution was successful, False otherwise
        """
        # Create execution configuration
        config = WriterExecutionConfig(
            writer_type=writer_type,
            exchange=exchange,
            pair=pair,
            start_date=start_date,
            end_date=end_date,
            parallel=self.config.get("parallel", False),
            additional_args=kwargs.get("additional_args"),
            checkpoint_key_suffix=kwargs.get("checkpoint_key_suffix")
        )
        
        # Get exchange formatter if needed
        exchange_formatter = self._get_exchange_formatter()
        
        # Execute writer
        return self.writer_executor.execute_writer(config, exchange_formatter)
    
    def execute_writers_parallel(self, writer_types: List[str], exchange: str, pairs: List[str],
                                start_date: str, end_date: str) -> Dict[str, Dict[str, bool]]:
        """
        Execute multiple writers in parallel for multiple pairs.
        
        Args:
            writer_types: List of writer types to execute
            exchange: Exchange name
            pairs: List of trading pairs
            start_date: Start date for execution
            end_date: End date for execution
            
        Returns:
            Dictionary of results by writer type and pair
        """
        # Create writer function that uses our execute_writer_for_pair method
        def writer_function(writer_type: str, exchange: str, pair: str, start_date: str, end_date: str) -> bool:
            return self.execute_writer_for_pair(writer_type, exchange, pair, start_date, end_date)
        
        return self.parallel_executor.execute_writers_parallel(
            writer_types, exchange, pairs, start_date, end_date, writer_function
        )
    
    def run_all_with_monitoring(self) -> Dict[str, Any]:
        """
        Run all operations with comprehensive monitoring.
        
        Returns:
            Dictionary with execution results and summary
        """
        # Start global execution tracking
        self.monitor.start_global_execution()
        
        try:
            # Execute the orchestrator-specific logic
            results = self.run_all_operations()
        finally:
            # End global execution tracking and generate summary
            self.monitor.end_global_execution()
            summary = self.monitor.generate_execution_summary()
            
            # Add summary to results if results is a dict
            if isinstance(results, dict):
                results["execution_summary"] = summary
            else:
                results = {"results": results, "execution_summary": summary}
        
        return results
    
    def cleanup_old_checkpoints(self, days_old: int = 30) -> int:
        """
        Clean up old checkpoint files.
        
        Args:
            days_old: Number of days after which checkpoints are considered old
            
        Returns:
            Number of checkpoints cleaned up
        """
        return self.checkpoint_manager.cleanup_old_checkpoints(days_old)
    
    def list_checkpoints(self, exchange: Optional[str] = None, 
                        pair: Optional[str] = None,
                        writer_type: Optional[str] = None) -> Dict[str, Dict[str, Any]]:
        """
        List checkpoints with optional filtering.
        
        Args:
            exchange: Optional exchange filter
            pair: Optional pair filter
            writer_type: Optional writer type filter
            
        Returns:
            Dictionary of checkpoint data
        """
        return self.checkpoint_manager.list_checkpoints(exchange, pair, writer_type)
    
    def validate_exchange_pairs(self) -> Dict[str, List[str]]:
        """
        Validate all exchange pairs and return any warnings.
        
        Returns:
            Dictionary of warnings by exchange
        """
        warnings_by_exchange = {}
        
        for exchange, pairs in self.config["pairs"].items():
            exchange_warnings = []
            
            # Check exchange support
            if not self.exchange_handler.validate_exchange_support(exchange, self.orchestrator_type):
                exchange_warnings.append(f"Exchange {exchange} may not support {self.orchestrator_type} trading")
            
            # Validate each pair
            for pair in pairs:
                pair_warnings = self.exchange_handler.validate_pair_format(exchange, pair, self.orchestrator_type)
                exchange_warnings.extend(pair_warnings)
            
            if exchange_warnings:
                warnings_by_exchange[exchange] = exchange_warnings
        
        return warnings_by_exchange
    
    def get_execution_statistics(self) -> Dict[str, Any]:
        """
        Get comprehensive execution statistics.
        
        Returns:
            Dictionary with execution statistics
        """
        return self.monitor.generate_execution_summary()
    
    @abstractmethod
    def run_all_operations(self) -> Dict[str, Any]:
        """
        Run all orchestrator-specific operations.
        
        This method must be implemented by subclasses to define
        the specific execution logic for each orchestrator type.
        
        Returns:
            Dictionary with execution results
        """
        pass
    
    @abstractmethod
    def _get_exchange_formatter(self) -> Optional[callable]:
        """
        Get exchange-specific formatter function.
        
        This method should be implemented by subclasses to provide
        exchange-specific formatting logic if needed.
        
        Returns:
            Exchange formatter function or None
        """
        pass
    
    @abstractmethod
    def get_writer_types(self) -> List[str]:
        """
        Get list of writer types supported by this orchestrator.
        
        Returns:
            List of writer type names
        """
        pass
