#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Parallel Execution Utilities

This module provides centralized parallel execution functionality for data orchestrators,
including thread pool management, task scheduling, and result aggregation.
"""

import concurrent.futures
from typing import Dict, List, Callable, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

from framework.utils.common import CommonUtils


@dataclass
class ParallelTask:
    """Configuration for a parallel task."""
    
    task_id: str
    function: Callable
    args: Tuple
    kwargs: Dict[str, Any]
    exchange: str
    pair: str
    writer_type: str


@dataclass
class ParallelExecutionResult:
    """Result of parallel execution."""
    
    task_id: str
    success: bool
    result: Any
    error: Optional[Exception] = None
    execution_time: Optional[float] = None


class ParallelExecutor:
    """
    Centralized parallel execution for orchestrators.
    
    This class handles thread pool management, task scheduling,
    and result aggregation for parallel writer execution.
    """
    
    def __init__(self, common_utils: CommonUtils, max_workers: int = 10):
        """
        Initialize the parallel executor.
        
        Args:
            common_utils: CommonUtils instance for logging
            max_workers: Maximum number of worker threads
        """
        self.common_utils = common_utils
        self.logger = common_utils.log
        self.max_workers = max_workers
    
    def execute_writers_parallel(self, writer_types: List[str], exchange: str, pairs: List[str],
                                start_date: str, end_date: str, 
                                writer_function: Callable) -> Dict[str, Dict[str, bool]]:
        """
        Execute multiple writers in parallel for multiple pairs.
        
        Args:
            writer_types: List of writer types to run
            exchange: Exchange name
            pairs: List of trading pairs
            start_date: Start date for execution
            end_date: End date for execution
            writer_function: Function to execute for each writer/pair combination
            
        Returns:
            Dictionary of results by writer type and pair
        """
        self.logger.info(f"Running {len(writer_types)} writers in parallel for {exchange} "
                        f"with {len(pairs)} pairs from {start_date} to {end_date}")
        
        results = {}
        
        # Run each writer type sequentially, but pairs in parallel
        for writer_type in writer_types:
            self.logger.info(f"Starting parallel execution for {writer_type}")
            
            # Initialize results for this writer type
            results[writer_type] = {}
            
            # Create tasks for all pairs
            tasks = []
            for pair in pairs:
                task = ParallelTask(
                    task_id=f"{exchange}_{pair}_{writer_type}",
                    function=writer_function,
                    args=(writer_type, exchange, pair, start_date, end_date),
                    kwargs={},
                    exchange=exchange,
                    pair=pair,
                    writer_type=writer_type
                )
                tasks.append(task)
            
            # Execute tasks in parallel
            task_results = self.execute_tasks_parallel(tasks)
            
            # Process results
            for task_result in task_results:
                # Extract pair from task_id
                pair = task_result.task_id.split('_')[1]  # Assumes format: exchange_pair_writer
                results[writer_type][pair] = task_result.success and task_result.result
        
        return results
    
    def execute_tasks_parallel(self, tasks: List[ParallelTask]) -> List[ParallelExecutionResult]:
        """
        Execute a list of tasks in parallel.
        
        Args:
            tasks: List of tasks to execute
            
        Returns:
            List of execution results
        """
        results = []
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_task = {
                executor.submit(self._execute_task, task): task
                for task in tasks
            }
            
            # Process results as they complete
            for future in concurrent.futures.as_completed(future_to_task):
                task = future_to_task[future]
                
                try:
                    result = future.result()
                    results.append(result)
                    
                    if result.success:
                        self.logger.info(f"Task {result.task_id} completed successfully")
                    else:
                        self.logger.error(f"Task {result.task_id} failed: {result.error}")
                        
                except Exception as e:
                    # This should not happen as _execute_task handles exceptions
                    error_result = ParallelExecutionResult(
                        task_id=task.task_id,
                        success=False,
                        result=None,
                        error=e
                    )
                    results.append(error_result)
                    self.logger.error(f"Unexpected error in task {task.task_id}: {str(e)}")
        
        return results
    
    def _execute_task(self, task: ParallelTask) -> ParallelExecutionResult:
        """
        Execute a single task with error handling and timing.
        
        Args:
            task: Task to execute
            
        Returns:
            Execution result
        """
        start_time = datetime.now()
        
        try:
            # Execute the task function
            result = task.function(*task.args, **task.kwargs)
            
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            return ParallelExecutionResult(
                task_id=task.task_id,
                success=True,
                result=result,
                execution_time=execution_time
            )
            
        except Exception as e:
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            self.logger.error(f"Error executing task {task.task_id}: {str(e)}")
            
            return ParallelExecutionResult(
                task_id=task.task_id,
                success=False,
                result=None,
                error=e,
                execution_time=execution_time
            )
    
    def execute_pairs_parallel(self, pairs: List[str], exchange: str, writer_type: str,
                              start_date: str, end_date: str,
                              pair_function: Callable) -> Dict[str, bool]:
        """
        Execute a single writer type for multiple pairs in parallel.
        
        Args:
            pairs: List of trading pairs
            exchange: Exchange name
            writer_type: Writer type to execute
            start_date: Start date for execution
            end_date: End date for execution
            pair_function: Function to execute for each pair
            
        Returns:
            Dictionary of results by pair
        """
        self.logger.info(f"Running {writer_type} for {len(pairs)} pairs in parallel on {exchange}")
        
        # Create tasks for all pairs
        tasks = []
        for pair in pairs:
            task = ParallelTask(
                task_id=f"{exchange}_{pair}_{writer_type}",
                function=pair_function,
                args=(writer_type, exchange, pair, start_date, end_date),
                kwargs={},
                exchange=exchange,
                pair=pair,
                writer_type=writer_type
            )
            tasks.append(task)
        
        # Execute tasks in parallel
        task_results = self.execute_tasks_parallel(tasks)
        
        # Process results
        results = {}
        for task_result in task_results:
            # Extract pair from task_id
            pair = task_result.task_id.split('_')[1]  # Assumes format: exchange_pair_writer
            results[pair] = task_result.success and task_result.result
        
        return results
    
    def execute_correlation_parallel(self, pairs: List[str], exchange: str, 
                                   timeframes: List[str], start_date: str, end_date: str,
                                   correlation_function: Callable) -> Dict[str, Dict[str, bool]]:
        """
        Execute correlation writer for multiple pairs and timeframes in parallel.
        
        Args:
            pairs: List of trading pairs
            exchange: Exchange name
            timeframes: List of timeframes to process
            start_date: Start date for execution
            end_date: End date for execution
            correlation_function: Function to execute correlation analysis
            
        Returns:
            Dictionary of results by pair and timeframe
        """
        self.logger.info(f"Running correlation analysis for {len(pairs)} pairs "
                        f"and {len(timeframes)} timeframes in parallel on {exchange}")
        
        results = {}
        
        # Create tasks for all pair/timeframe combinations
        tasks = []
        for pair in pairs:
            for timeframe in timeframes:
                task = ParallelTask(
                    task_id=f"{exchange}_{pair}_correlation_{timeframe}",
                    function=correlation_function,
                    args=(exchange, pair, start_date, end_date, timeframe),
                    kwargs={},
                    exchange=exchange,
                    pair=pair,
                    writer_type=f"correlation_{timeframe}"
                )
                tasks.append(task)
        
        # Execute tasks in parallel
        task_results = self.execute_tasks_parallel(tasks)
        
        # Process results
        for task_result in task_results:
            # Extract pair and timeframe from task_id
            parts = task_result.task_id.split('_')
            pair = parts[1]
            timeframe = parts[3]  # Assumes format: exchange_pair_correlation_timeframe
            
            if pair not in results:
                results[pair] = {}
            
            results[pair][timeframe] = task_result.success and task_result.result
        
        return results
    
    def get_execution_statistics(self, results: List[ParallelExecutionResult]) -> Dict[str, Any]:
        """
        Generate execution statistics from parallel execution results.
        
        Args:
            results: List of execution results
            
        Returns:
            Dictionary with execution statistics
        """
        total_tasks = len(results)
        successful_tasks = sum(1 for r in results if r.success)
        failed_tasks = total_tasks - successful_tasks
        
        execution_times = [r.execution_time for r in results if r.execution_time is not None]
        
        stats = {
            "total_tasks": total_tasks,
            "successful_tasks": successful_tasks,
            "failed_tasks": failed_tasks,
            "success_rate": (successful_tasks / total_tasks * 100) if total_tasks > 0 else 0,
            "total_execution_time": sum(execution_times) if execution_times else 0,
            "average_execution_time": sum(execution_times) / len(execution_times) if execution_times else 0,
            "min_execution_time": min(execution_times) if execution_times else 0,
            "max_execution_time": max(execution_times) if execution_times else 0
        }
        
        return stats
