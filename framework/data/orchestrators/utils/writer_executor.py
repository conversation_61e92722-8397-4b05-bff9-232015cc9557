#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Writer Execution Utilities

This module provides centralized writer execution functionality for data orchestrators,
including argument preparation, module loading, and execution with monitoring.
"""

import importlib
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass

from framework.utils.common import CommonUtils
from .orchestrator_monitoring import OrchestratorMonitor
from .checkpoint_manager import CheckpointManager


@dataclass
class WriterExecutionConfig:
    """Configuration for writer execution."""
    
    writer_type: str
    exchange: str
    pair: str
    start_date: str
    end_date: str
    parallel: bool = False
    additional_args: Optional[Dict[str, Any]] = None
    checkpoint_key_suffix: Optional[str] = None


class WriterExecutor:
    """
    Centralized writer execution for orchestrators.
    
    This class handles writer module loading, argument preparation,
    execution with monitoring, and checkpoint management.
    """
    
    def __init__(self, common_utils: CommonUtils, monitor: OrchestratorMonitor,
                 checkpoint_manager: CheckpointManager):
        """
        Initialize the writer executor.
        
        Args:
            common_utils: CommonUtils instance for logging
            monitor: OrchestratorMonitor for execution tracking
            checkpoint_manager: CheckpointManager for checkpoint operations
        """
        self.common_utils = common_utils
        self.logger = common_utils.log
        self.monitor = monitor
        self.checkpoint_manager = checkpoint_manager
        
        # Cache for loaded writer modules
        self._writer_modules: Dict[str, Any] = {}
    
    def load_writer_module(self, writer_type: str) -> Any:
        """
        Load a writer module with caching.
        
        Args:
            writer_type: Writer type to load
            
        Returns:
            Loaded writer module
            
        Raises:
            ImportError: If writer module cannot be loaded
        """
        if writer_type not in self._writer_modules:
            try:
                module = importlib.import_module(f"framework.data.writers.{writer_type}")
                self._writer_modules[writer_type] = module
                self.logger.debug(f"Loaded writer module: {writer_type}")
            except ImportError as e:
                self.logger.error(f"Failed to load writer module {writer_type}: {str(e)}")
                raise
        
        return self._writer_modules[writer_type]
    
    def prepare_writer_arguments(self, config: WriterExecutionConfig) -> List[str]:
        """
        Prepare arguments for writer execution.
        
        Args:
            config: Writer execution configuration
            
        Returns:
            List of command line arguments for the writer
        """
        args = [
            "-s", config.start_date,
            "-e", config.end_date,
            "-i", config.pair,
            "-x", config.exchange
        ]
        
        # Add parallel flag if enabled
        if config.parallel:
            args.append("-p")
        
        # Add additional arguments if provided
        if config.additional_args:
            for key, value in config.additional_args.items():
                args.extend([f"-{key}", str(value)])
        
        return args
    
    def prepare_correlation_writer_arguments(self, config: WriterExecutionConfig,
                                           timeframe: str) -> List[str]:
        """
        Prepare arguments specifically for correlation writer.
        
        Args:
            config: Writer execution configuration
            timeframe: Timeframe for correlation analysis
            
        Returns:
            List of command line arguments for the correlation writer
        """
        args = [
            "-s", config.start_date,
            "-e", config.end_date,
            "-p", config.pair,  # Correlation writer uses -p instead of -i
            "-x", config.exchange,
            "-t", timeframe,    # Step size (timeframe)
            "-l", timeframe     # Lookback period
        ]
        
        # Add parallel flag if enabled (correlation writer uses -P)
        if config.parallel:
            args.append("-P")
        
        return args
    
    def execute_writer(self, config: WriterExecutionConfig,
                      exchange_formatter: Optional[Callable[[str, str], Dict[str, Any]]] = None) -> bool:
        """
        Execute a writer with full monitoring and checkpoint support.
        
        Args:
            config: Writer execution configuration
            exchange_formatter: Optional function to format exchange-specific data
            
        Returns:
            True if execution was successful, False otherwise
        """
        # Apply exchange-specific formatting if provided
        formatted_pair = config.pair
        if exchange_formatter:
            formatting_result = exchange_formatter(config.exchange, config.pair)
            formatted_pair = formatting_result.get("pair", config.pair)
        
        # Generate checkpoint key
        checkpoint_key = config.checkpoint_key_suffix or config.writer_type
        
        try:
            # Use enhanced monitoring for execution tracking
            with self.monitor.track_execution_time(config.writer_type, config.exchange, formatted_pair) as metrics:
                # Load writer module
                writer_module = self.load_writer_module(config.writer_type)
                
                # Report progress start
                self.monitor.report_progress(config.exchange, formatted_pair, config.writer_type, 0.0)
                
                # Load checkpoint if exists
                checkpoint = self.checkpoint_manager.load_checkpoint(
                    config.exchange, formatted_pair, checkpoint_key
                )
                
                # Adjust start date if checkpoint exists
                start_date = config.start_date
                if checkpoint and "end_date" in checkpoint:
                    start_date = checkpoint["end_date"]
                    self.logger.info(f"Resuming {config.writer_type} for {config.exchange} {formatted_pair} "
                                   f"from {start_date} to {config.end_date}")
                
                # Update config with potentially adjusted start date
                execution_config = WriterExecutionConfig(
                    writer_type=config.writer_type,
                    exchange=config.exchange,
                    pair=formatted_pair,
                    start_date=start_date,
                    end_date=config.end_date,
                    parallel=config.parallel,
                    additional_args=config.additional_args
                )
                
                # Prepare writer arguments
                writer_args = self.prepare_writer_arguments(execution_config)
                
                # Report progress mid-way
                self.monitor.report_progress(config.exchange, formatted_pair, config.writer_type, 0.5)
                
                # Execute writer
                result = writer_module.main(writer_args)
                
                # Report progress completion
                self.monitor.report_progress(config.exchange, formatted_pair, config.writer_type, 1.0)
                
                # Save checkpoint if successful
                if result:
                    self.checkpoint_manager.save_checkpoint(
                        config.exchange, formatted_pair, checkpoint_key, config.end_date
                    )
                
                return bool(result)
                
        except ImportError as e:
            # Enhanced error handling for import errors
            can_continue = self.monitor.handle_writer_error(e, config.writer_type, config.exchange, config.pair)
            return False
        except Exception as e:
            # Enhanced error handling for other errors
            can_continue = self.monitor.handle_writer_error(e, config.writer_type, config.exchange, config.pair)
            return False
    
    def execute_correlation_writer(self, config: WriterExecutionConfig, timeframe: str) -> bool:
        """
        Execute correlation writer with specific argument handling.
        
        Args:
            config: Writer execution configuration
            timeframe: Timeframe for correlation analysis
            
        Returns:
            True if execution was successful, False otherwise
        """
        # Generate checkpoint key with timeframe
        checkpoint_key = f"{config.writer_type}_{timeframe}"
        writer_type_with_timeframe = f"{config.writer_type}_{timeframe}"
        
        try:
            # Use enhanced monitoring for execution tracking
            with self.monitor.track_execution_time(writer_type_with_timeframe, config.exchange, config.pair) as metrics:
                # Load writer module
                writer_module = self.load_writer_module(config.writer_type)
                writer_main = getattr(writer_module, "main")
                
                # Report progress start
                self.monitor.report_progress(config.exchange, config.pair, writer_type_with_timeframe, 0.0)
                
                # Load checkpoint if exists
                checkpoint = self.checkpoint_manager.load_checkpoint(
                    config.exchange, config.pair, checkpoint_key
                )
                
                # Adjust start date if checkpoint exists
                start_date = config.start_date
                if checkpoint and "end_date" in checkpoint:
                    start_date = checkpoint["end_date"]
                    self.logger.info(f"Resuming {config.writer_type} for {config.exchange} {config.pair} "
                                   f"with timeframe {timeframe} from {start_date} to {config.end_date}")
                
                # Prepare correlation writer arguments
                execution_config = WriterExecutionConfig(
                    writer_type=config.writer_type,
                    exchange=config.exchange,
                    pair=config.pair,
                    start_date=start_date,
                    end_date=config.end_date,
                    parallel=config.parallel
                )
                
                writer_args = self.prepare_correlation_writer_arguments(execution_config, timeframe)
                
                # Report progress mid-way
                self.monitor.report_progress(config.exchange, config.pair, writer_type_with_timeframe, 0.5)
                
                # Execute writer
                result = writer_main(writer_args)
                
                # Report progress completion
                self.monitor.report_progress(config.exchange, config.pair, writer_type_with_timeframe, 1.0)
                
                # Save checkpoint if successful
                if result:
                    self.checkpoint_manager.save_checkpoint(
                        config.exchange, config.pair, checkpoint_key, config.end_date
                    )
                
                return bool(result)
                
        except Exception as e:
            # Enhanced error handling
            can_continue = self.monitor.handle_writer_error(e, writer_type_with_timeframe, config.exchange, config.pair)
            return False
    
    def clear_module_cache(self) -> None:
        """Clear the writer module cache."""
        self._writer_modules.clear()
        self.logger.debug("Cleared writer module cache")
    
    def get_cached_modules(self) -> List[str]:
        """
        Get list of cached writer modules.
        
        Returns:
            List of cached writer module names
        """
        return list(self._writer_modules.keys())
