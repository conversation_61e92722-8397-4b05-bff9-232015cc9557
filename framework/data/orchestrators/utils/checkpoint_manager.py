#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Checkpoint Management Utilities

This module provides centralized checkpoint management functionality for data orchestrators,
including saving, loading, validation, and cleanup operations.
"""

import os
import json
import hashlib
from datetime import datetime
from typing import Dict, Optional, Any
from pathlib import Path

import pytz

from framework.utils.common import CommonUtils


class CheckpointManager:
    """
    Centralized checkpoint management for orchestrators.
    
    This class handles all checkpoint operations including saving, loading,
    validation, and cleanup with support for different checkpoint formats.
    """
    
    def __init__(self, common_utils: CommonUtils, checkpoint_dir: str, config: Dict[str, Any]):
        """
        Initialize the checkpoint manager.
        
        Args:
            common_utils: CommonUtils instance for logging
            checkpoint_dir: Directory to store checkpoint files
            config: Configuration dictionary for hash generation
        """
        self.common_utils = common_utils
        self.logger = common_utils.log
        self.checkpoint_dir = checkpoint_dir
        self.config = config
        
        # Ensure checkpoint directory exists
        os.makedirs(self.checkpoint_dir, exist_ok=True)
    
    def generate_checkpoint_key(self, exchange: str, pair: str, writer_type: str, 
                               additional_key: Optional[str] = None) -> str:
        """
        Generate a unique key for a checkpoint.
        
        Args:
            exchange: Exchange name
            pair: Trading pair
            writer_type: Writer type
            additional_key: Optional additional key component (e.g., timeframe)
            
        Returns:
            Unique checkpoint key
        """
        if additional_key:
            return f"{exchange}_{pair}_{writer_type}_{additional_key}"
        return f"{exchange}_{pair}_{writer_type}"
    
    def generate_config_hash(self) -> str:
        """
        Generate a hash of the configuration for checkpoint validation.
        
        Returns:
            Hash string of the configuration
        """
        # Create a copy of config without volatile fields
        config_for_hash = {k: v for k, v in self.config.items() 
                          if k not in ['timestamp', 'execution_id']}
        config_str = json.dumps(config_for_hash, sort_keys=True)
        return hashlib.md5(config_str.encode()).hexdigest()
    
    def get_checkpoint_path(self, checkpoint_key: str, format_type: str = "json") -> str:
        """
        Get the path to a checkpoint file.
        
        Args:
            checkpoint_key: Checkpoint key
            format_type: File format (json or csv)
            
        Returns:
            Path to checkpoint file
        """
        extension = "json" if format_type == "json" else "csv"
        return os.path.join(self.checkpoint_dir, f"{checkpoint_key}.{extension}")
    
    def save_checkpoint(self, exchange: str, pair: str, writer_type: str, 
                       end_date: str, additional_key: Optional[str] = None,
                       additional_data: Optional[Dict[str, Any]] = None) -> bool:
        """
        Save a checkpoint for a writer execution.
        
        Args:
            exchange: Exchange name
            pair: Trading pair
            writer_type: Writer type
            end_date: Last processed date
            additional_key: Optional additional key component
            additional_data: Optional additional data to store
            
        Returns:
            True if checkpoint was saved successfully, False otherwise
        """
        try:
            checkpoint_key = self.generate_checkpoint_key(exchange, pair, writer_type, additional_key)
            checkpoint_path = self.get_checkpoint_path(checkpoint_key)
            
            checkpoint_data = {
                "exchange": exchange,
                "pair": pair,
                "writer_type": writer_type,
                "end_date": end_date,
                "config_hash": self.generate_config_hash(),
                "timestamp": datetime.now(pytz.UTC).strftime("%Y-%m-%d-%H:%M:%S"),
                "checkpoint_key": checkpoint_key
            }
            
            # Add additional key if provided
            if additional_key:
                checkpoint_data["additional_key"] = additional_key
            
            # Add additional data if provided
            if additional_data:
                checkpoint_data.update(additional_data)
            
            # Save as JSON
            with open(checkpoint_path, 'w') as f:
                json.dump(checkpoint_data, f, indent=2)
            
            self.logger.info(f"Saved checkpoint for {checkpoint_key} at {end_date}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving checkpoint for {exchange} {pair} {writer_type}: {str(e)}")
            return False
    
    def load_checkpoint(self, exchange: str, pair: str, writer_type: str,
                       additional_key: Optional[str] = None,
                       validate_config: bool = True) -> Optional[Dict[str, Any]]:
        """
        Load a checkpoint for a writer execution.
        
        Args:
            exchange: Exchange name
            pair: Trading pair
            writer_type: Writer type
            additional_key: Optional additional key component
            validate_config: Whether to validate config hash
            
        Returns:
            Checkpoint data or None if no valid checkpoint exists
        """
        try:
            checkpoint_key = self.generate_checkpoint_key(exchange, pair, writer_type, additional_key)
            checkpoint_path = self.get_checkpoint_path(checkpoint_key)
            
            if not os.path.exists(checkpoint_path):
                # Try legacy checkpoint format
                legacy_key = f"{exchange}_{pair}_{writer_type}"
                legacy_path = self.get_checkpoint_path(legacy_key)
                if os.path.exists(legacy_path):
                    checkpoint_path = legacy_path
                    checkpoint_key = legacy_key
                else:
                    return None
            
            with open(checkpoint_path, 'r') as f:
                checkpoint_data = json.load(f)
            
            # Validate config hash if requested
            if validate_config and checkpoint_data.get("config_hash") != self.generate_config_hash():
                self.logger.warning(f"Checkpoint config hash mismatch for {checkpoint_key}, ignoring checkpoint")
                return None
            
            self.logger.info(f"Loaded checkpoint for {checkpoint_key} at {checkpoint_data.get('end_date')}")
            return checkpoint_data
            
        except Exception as e:
            self.logger.error(f"Error loading checkpoint for {exchange} {pair} {writer_type}: {str(e)}")
            return None
    
    def delete_checkpoint(self, exchange: str, pair: str, writer_type: str,
                         additional_key: Optional[str] = None) -> bool:
        """
        Delete a checkpoint file.
        
        Args:
            exchange: Exchange name
            pair: Trading pair
            writer_type: Writer type
            additional_key: Optional additional key component
            
        Returns:
            True if checkpoint was deleted successfully, False otherwise
        """
        try:
            checkpoint_key = self.generate_checkpoint_key(exchange, pair, writer_type, additional_key)
            checkpoint_path = self.get_checkpoint_path(checkpoint_key)
            
            if os.path.exists(checkpoint_path):
                os.remove(checkpoint_path)
                self.logger.info(f"Deleted checkpoint for {checkpoint_key}")
                return True
            else:
                self.logger.warning(f"Checkpoint file not found for {checkpoint_key}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error deleting checkpoint for {exchange} {pair} {writer_type}: {str(e)}")
            return False
    
    def list_checkpoints(self, exchange: Optional[str] = None, 
                        pair: Optional[str] = None,
                        writer_type: Optional[str] = None) -> Dict[str, Dict[str, Any]]:
        """
        List all checkpoints, optionally filtered by exchange, pair, or writer type.
        
        Args:
            exchange: Optional exchange filter
            pair: Optional pair filter
            writer_type: Optional writer type filter
            
        Returns:
            Dictionary of checkpoint data keyed by checkpoint key
        """
        checkpoints = {}
        
        try:
            for filename in os.listdir(self.checkpoint_dir):
                if not filename.endswith('.json'):
                    continue
                
                checkpoint_key = filename[:-5]  # Remove .json extension
                checkpoint_path = os.path.join(self.checkpoint_dir, filename)
                
                try:
                    with open(checkpoint_path, 'r') as f:
                        checkpoint_data = json.load(f)
                    
                    # Apply filters
                    if exchange and checkpoint_data.get('exchange') != exchange:
                        continue
                    if pair and checkpoint_data.get('pair') != pair:
                        continue
                    if writer_type and checkpoint_data.get('writer_type') != writer_type:
                        continue
                    
                    checkpoints[checkpoint_key] = checkpoint_data
                    
                except Exception as e:
                    self.logger.warning(f"Error reading checkpoint file {filename}: {str(e)}")
                    continue
        
        except Exception as e:
            self.logger.error(f"Error listing checkpoints: {str(e)}")
        
        return checkpoints
    
    def cleanup_old_checkpoints(self, days_old: int = 30) -> int:
        """
        Clean up checkpoint files older than specified days.
        
        Args:
            days_old: Number of days after which checkpoints are considered old
            
        Returns:
            Number of checkpoints cleaned up
        """
        cleaned_count = 0
        cutoff_timestamp = datetime.now(pytz.UTC).timestamp() - (days_old * 24 * 60 * 60)
        
        try:
            for filename in os.listdir(self.checkpoint_dir):
                if not filename.endswith('.json'):
                    continue
                
                checkpoint_path = os.path.join(self.checkpoint_dir, filename)
                file_timestamp = os.path.getmtime(checkpoint_path)
                
                if file_timestamp < cutoff_timestamp:
                    try:
                        os.remove(checkpoint_path)
                        cleaned_count += 1
                        self.logger.info(f"Cleaned up old checkpoint: {filename}")
                    except Exception as e:
                        self.logger.warning(f"Error cleaning up checkpoint {filename}: {str(e)}")
        
        except Exception as e:
            self.logger.error(f"Error during checkpoint cleanup: {str(e)}")
        
        self.logger.info(f"Cleaned up {cleaned_count} old checkpoint files")
        return cleaned_count
