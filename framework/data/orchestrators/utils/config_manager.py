#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Configuration Management Utilities

This module provides centralized configuration management functionality for data orchestrators,
including validation, loading, and default value handling.
"""

import json
import os
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from pathlib import Path

from framework.utils.common import CommonUtils


@dataclass
class ConfigValidationResult:
    """Result of configuration validation."""
    
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    normalized_config: Optional[Dict[str, Any]] = None


class ConfigManager:
    """
    Centralized configuration management for orchestrators.
    
    This class handles configuration loading, validation, normalization,
    and default value management for data orchestrators.
    """
    
    def __init__(self, common_utils: CommonUtils):
        """
        Initialize the configuration manager.
        
        Args:
            common_utils: CommonUtils instance for logging
        """
        self.common_utils = common_utils
        self.logger = common_utils.log
        
        # Default configuration templates
        self.default_spot_config = {
            "start_date": "2024-01-01-00:00:00",
            "end_date": "2024-01-02-00:00:00",
            "parallel": False,
            "max_workers": 5,
            "checkpoint_enabled": True,
            "checkpoint_format": "json",
            "retry_failed": True,
            "retry_attempts": 3,
            "pairs": {}
        }
        
        self.default_futures_config = {
            "start_date": "2024-01-01-00:00:00",
            "end_date": "2024-01-02-00:00:00",
            "parallel": False,
            "max_workers": 10,
            "checkpoint_enabled": True,
            "checkpoint_format": "json",
            "retry_failed": True,
            "retry_attempts": 3,
            "pairs": {}
        }
        
        # Required configuration fields
        self.required_fields = [
            "start_date",
            "end_date",
            "pairs"
        ]
        
        # Valid configuration values
        self.valid_values = {
            "checkpoint_format": ["json", "csv"],
            "parallel": [True, False],
            "checkpoint_enabled": [True, False],
            "retry_failed": [True, False]
        }
    
    def load_config_from_file(self, config_path: str) -> Dict[str, Any]:
        """
        Load configuration from a JSON file.
        
        Args:
            config_path: Path to configuration file
            
        Returns:
            Configuration dictionary
            
        Raises:
            FileNotFoundError: If config file doesn't exist
            json.JSONDecodeError: If config file is invalid JSON
        """
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            self.logger.info(f"Loaded configuration from {config_path}")
            return config
            
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON in configuration file {config_path}: {str(e)}")
            raise
        except Exception as e:
            self.logger.error(f"Error loading configuration file {config_path}: {str(e)}")
            raise
    
    def validate_config(self, config: Dict[str, Any], orchestrator_type: str = "spot") -> ConfigValidationResult:
        """
        Validate configuration for an orchestrator.
        
        Args:
            config: Configuration dictionary to validate
            orchestrator_type: Type of orchestrator (spot or futures)
            
        Returns:
            ConfigValidationResult with validation details
        """
        result = ConfigValidationResult(is_valid=True)
        errors = []
        warnings = []
        
        # Check required fields
        for field in self.required_fields:
            if field not in config:
                errors.append(f"Missing required field: {field}")
        
        # Validate field values
        for field, valid_options in self.valid_values.items():
            if field in config and config[field] not in valid_options:
                errors.append(f"Invalid value for {field}: {config[field]}. Valid options: {valid_options}")
        
        # Validate date format
        for date_field in ["start_date", "end_date"]:
            if date_field in config:
                if not self._validate_date_format(config[date_field]):
                    errors.append(f"Invalid date format for {date_field}: {config[date_field]}. "
                                f"Expected format: YYYY-MM-DD-HH:MM:SS")
        
        # Validate pairs structure
        if "pairs" in config:
            if not isinstance(config["pairs"], dict):
                errors.append("Pairs must be a dictionary")
            else:
                for exchange, pairs in config["pairs"].items():
                    if not isinstance(pairs, list):
                        errors.append(f"Pairs for exchange {exchange} must be a list")
                    elif len(pairs) == 0:
                        warnings.append(f"No pairs specified for exchange {exchange}")
        
        # Validate numeric fields
        numeric_fields = ["max_workers", "retry_attempts"]
        for field in numeric_fields:
            if field in config:
                if not isinstance(config[field], int) or config[field] < 1:
                    errors.append(f"{field} must be a positive integer")
        
        # Orchestrator-specific validation
        if orchestrator_type == "futures":
            self._validate_futures_specific(config, errors, warnings)
        else:
            self._validate_spot_specific(config, errors, warnings)
        
        # Set result
        result.errors = errors
        result.warnings = warnings
        result.is_valid = len(errors) == 0
        
        # Create normalized config if valid
        if result.is_valid:
            result.normalized_config = self.normalize_config(config, orchestrator_type)
        
        return result
    
    def normalize_config(self, config: Dict[str, Any], orchestrator_type: str = "spot") -> Dict[str, Any]:
        """
        Normalize configuration by applying defaults and standardizing values.
        
        Args:
            config: Configuration dictionary to normalize
            orchestrator_type: Type of orchestrator (spot or futures)
            
        Returns:
            Normalized configuration dictionary
        """
        # Start with appropriate defaults
        if orchestrator_type == "futures":
            normalized = self.default_futures_config.copy()
        else:
            normalized = self.default_spot_config.copy()
        
        # Update with provided config
        normalized.update(config)
        
        # Ensure numeric fields are integers
        numeric_fields = ["max_workers", "retry_attempts"]
        for field in numeric_fields:
            if field in normalized and isinstance(normalized[field], (int, float)):
                normalized[field] = int(normalized[field])
        
        # Ensure boolean fields are booleans
        boolean_fields = ["parallel", "checkpoint_enabled", "retry_failed"]
        for field in boolean_fields:
            if field in normalized:
                if isinstance(normalized[field], str):
                    normalized[field] = normalized[field].lower() in ['true', '1', 'yes', 'on']
                else:
                    normalized[field] = bool(normalized[field])
        
        return normalized
    
    def get_default_config(self, orchestrator_type: str = "spot") -> Dict[str, Any]:
        """
        Get default configuration for an orchestrator type.
        
        Args:
            orchestrator_type: Type of orchestrator (spot or futures)
            
        Returns:
            Default configuration dictionary
        """
        if orchestrator_type == "futures":
            return self.default_futures_config.copy()
        else:
            return self.default_spot_config.copy()
    
    def _validate_date_format(self, date_str: str) -> bool:
        """
        Validate date format (YYYY-MM-DD-HH:MM:SS).
        
        Args:
            date_str: Date string to validate
            
        Returns:
            True if format is valid, False otherwise
        """
        try:
            from datetime import datetime
            datetime.strptime(date_str, "%Y-%m-%d-%H:%M:%S")
            return True
        except ValueError:
            return False
    
    def _validate_spot_specific(self, config: Dict[str, Any], errors: List[str], warnings: List[str]) -> None:
        """Validate spot-specific configuration."""
        pass
    
    def _validate_futures_specific(self, config: Dict[str, Any], errors: List[str], warnings: List[str]) -> None:
        """Validate futures-specific configuration."""
        if "pairs" in config:
            for exchange, pairs in config["pairs"].items():
                if isinstance(pairs, list):
                    for pair in pairs:
                        if isinstance(pair, str) and not pair.isupper():
                            warnings.append(f"Futures pair {pair} for {exchange} should typically be uppercase")
