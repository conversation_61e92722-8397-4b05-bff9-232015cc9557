#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Exchange-specific Handling Utilities

This module provides centralized exchange-specific formatting and validation
functionality for data orchestrators.
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from framework.utils.common import CommonUtils


@dataclass
class ExchangeFormatResult:
    """Result of exchange-specific formatting."""
    
    pair: str
    contract_type: Optional[str] = None
    formatted_pair: Optional[str] = None
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.warnings is None:
            self.warnings = []


class ExchangeHandler:
    """
    Centralized exchange-specific handling for orchestrators.
    
    This class handles exchange-specific formatting, validation,
    and pair normalization for different market types.
    """
    
    def __init__(self, common_utils: CommonUtils):
        """
        Initialize the exchange handler.
        
        Args:
            common_utils: CommonUtils instance for logging
        """
        self.common_utils = common_utils
        self.logger = common_utils.log
        
        # Exchange-specific configuration
        self.exchange_configs = {
            "binance": {
                "spot_pair_format": "lowercase",
                "futures_pair_format": "uppercase",
                "separator": "_",
                "supports_futures": True,
                "supports_spot": True
            },
            "coinbase": {
                "spot_pair_format": "uppercase",
                "futures_pair_format": "uppercase",
                "separator": "_",
                "supports_futures": False,
                "supports_spot": True
            },
            "kraken": {
                "spot_pair_format": "uppercase",
                "futures_pair_format": "uppercase",
                "separator": "_",
                "supports_futures": True,
                "supports_spot": True
            },
            "okx": {
                "spot_pair_format": "uppercase",
                "futures_pair_format": "uppercase",
                "separator": "_",
                "supports_futures": True,
                "supports_spot": True
            }
        }
    
    def validate_exchange_support(self, exchange: str, market_type: str) -> bool:
        """
        Validate if an exchange supports a specific market type.
        
        Args:
            exchange: Exchange name
            market_type: Market type (spot or futures)
            
        Returns:
            True if exchange supports the market type, False otherwise
        """
        if exchange not in self.exchange_configs:
            self.logger.warning(f"Unknown exchange: {exchange}")
            return True  # Assume support for unknown exchanges
        
        config = self.exchange_configs[exchange]
        
        if market_type == "spot":
            return config.get("supports_spot", True)
        elif market_type == "futures":
            return config.get("supports_futures", True)
        else:
            self.logger.warning(f"Unknown market type: {market_type}")
            return True
    
    def format_spot_pair(self, exchange: str, pair: str) -> ExchangeFormatResult:
        """
        Format a trading pair for spot markets according to exchange requirements.
        
        Args:
            exchange: Exchange name
            pair: Trading pair
            
        Returns:
            ExchangeFormatResult with formatted pair and any warnings
        """
        result = ExchangeFormatResult(pair=pair)
        
        if exchange not in self.exchange_configs:
            self.logger.warning(f"Unknown exchange {exchange}, using pair as-is")
            return result
        
        config = self.exchange_configs[exchange]
        
        # Check if exchange supports spot trading
        if not config.get("supports_spot", True):
            result.warnings.append(f"Exchange {exchange} may not support spot trading")
        
        # Apply formatting based on exchange requirements
        format_type = config.get("spot_pair_format", "lowercase")
        
        if format_type == "lowercase":
            formatted_pair = pair.lower()
            if pair != formatted_pair:
                result.warnings.append(f"Converted pair from {pair} to {formatted_pair} for {exchange}")
        elif format_type == "uppercase":
            formatted_pair = pair.upper()
            if pair != formatted_pair:
                result.warnings.append(f"Converted pair from {pair} to {formatted_pair} for {exchange}")
        else:
            formatted_pair = pair
        
        result.pair = formatted_pair
        result.formatted_pair = formatted_pair
        
        return result
    
    def format_futures_pair(self, exchange: str, pair: str) -> ExchangeFormatResult:
        """
        Format a trading pair for futures markets according to exchange requirements.
        
        Args:
            exchange: Exchange name
            pair: Trading pair
            
        Returns:
            ExchangeFormatResult with formatted pair and any warnings
        """
        result = ExchangeFormatResult(pair=pair, contract_type="perpetual")
        
        if exchange not in self.exchange_configs:
            self.logger.warning(f"Unknown exchange {exchange}, using pair as-is")
            return result
        
        config = self.exchange_configs[exchange]
        
        # Check if exchange supports futures trading
        if not config.get("supports_futures", True):
            result.warnings.append(f"Exchange {exchange} may not support futures trading")
        
        # Apply formatting based on exchange requirements
        format_type = config.get("futures_pair_format", "uppercase")
        
        if format_type == "uppercase":
            formatted_pair = pair.upper()
            if pair != formatted_pair:
                result.warnings.append(f"Converted futures pair from {pair} to {formatted_pair} for {exchange}")
        elif format_type == "lowercase":
            formatted_pair = pair.lower()
            if pair != formatted_pair:
                result.warnings.append(f"Converted futures pair from {pair} to {formatted_pair} for {exchange}")
        else:
            formatted_pair = pair
        
        # Additional futures-specific validation
        if exchange == "binance" and not formatted_pair.isupper():
            result.warnings.append(f"Binance futures pairs are typically uppercase: {formatted_pair}")
        
        result.pair = formatted_pair
        result.formatted_pair = formatted_pair
        
        return result
    
    def validate_pair_format(self, exchange: str, pair: str, market_type: str) -> List[str]:
        """
        Validate pair format for a specific exchange and market type.
        
        Args:
            exchange: Exchange name
            pair: Trading pair
            market_type: Market type (spot or futures)
            
        Returns:
            List of validation warnings
        """
        warnings = []
        
        if exchange not in self.exchange_configs:
            warnings.append(f"Unknown exchange: {exchange}")
            return warnings
        
        config = self.exchange_configs[exchange]
        
        # Check market type support
        if not self.validate_exchange_support(exchange, market_type):
            warnings.append(f"Exchange {exchange} may not support {market_type} trading")
        
        # Check pair format
        if market_type == "spot":
            expected_format = config.get("spot_pair_format", "lowercase")
        else:
            expected_format = config.get("futures_pair_format", "uppercase")
        
        if expected_format == "lowercase" and pair != pair.lower():
            warnings.append(f"Pair {pair} should be lowercase for {exchange} {market_type}")
        elif expected_format == "uppercase" and pair != pair.upper():
            warnings.append(f"Pair {pair} should be uppercase for {exchange} {market_type}")
        
        # Check separator
        expected_separator = config.get("separator", "_")
        if expected_separator not in pair:
            warnings.append(f"Pair {pair} should use separator '{expected_separator}' for {exchange}")
        
        return warnings
    
    def normalize_pair_list(self, exchange: str, pairs: List[str], market_type: str) -> Dict[str, ExchangeFormatResult]:
        """
        Normalize a list of trading pairs for a specific exchange and market type.
        
        Args:
            exchange: Exchange name
            pairs: List of trading pairs
            market_type: Market type (spot or futures)
            
        Returns:
            Dictionary mapping original pairs to format results
        """
        results = {}
        
        for pair in pairs:
            if market_type == "spot":
                result = self.format_spot_pair(exchange, pair)
            else:
                result = self.format_futures_pair(exchange, pair)
            
            results[pair] = result
            
            # Log warnings
            for warning in result.warnings:
                self.logger.warning(warning)
        
        return results
    
    def get_exchange_info(self, exchange: str) -> Dict[str, Any]:
        """
        Get configuration information for an exchange.
        
        Args:
            exchange: Exchange name
            
        Returns:
            Dictionary with exchange configuration
        """
        if exchange in self.exchange_configs:
            return self.exchange_configs[exchange].copy()
        else:
            return {
                "spot_pair_format": "unknown",
                "futures_pair_format": "unknown",
                "separator": "_",
                "supports_futures": True,
                "supports_spot": True
            }
    
    def add_exchange_config(self, exchange: str, config: Dict[str, Any]) -> None:
        """
        Add or update configuration for an exchange.
        
        Args:
            exchange: Exchange name
            config: Exchange configuration dictionary
        """
        self.exchange_configs[exchange] = config
        self.logger.info(f"Added/updated configuration for exchange: {exchange}")
    
    def list_supported_exchanges(self) -> List[str]:
        """
        Get list of supported exchanges.
        
        Returns:
            List of exchange names
        """
        return list(self.exchange_configs.keys())
