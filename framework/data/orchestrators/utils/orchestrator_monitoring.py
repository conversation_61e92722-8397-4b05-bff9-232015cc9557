#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Orchestrator Monitoring and Logging Utilities

This module provides comprehensive logging, error handling, progress tracking,
and execution monitoring functionality for data orchestrators.
"""

import time
import traceback
from contextlib import contextmanager
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field

import pytz

from framework.utils.common import CommonUtils


@dataclass
class ExecutionMetrics:
    """Data class to track execution metrics for orchestrator operations."""
    
    # Timing metrics
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    execution_time: Optional[float] = None
    
    # Progress metrics
    total_operations: int = 0
    completed_operations: int = 0
    failed_operations: int = 0
    
    # Operation details
    exchange: Optional[str] = None
    pair: Optional[str] = None
    writer_type: Optional[str] = None
    
    # Error tracking
    errors: List[Dict[str, Any]] = field(default_factory=list)
    warnings: List[Dict[str, Any]] = field(default_factory=list)
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate as a percentage."""
        if self.total_operations == 0:
            return 0.0
        return (self.completed_operations / self.total_operations) * 100.0
    
    @property
    def progress_percentage(self) -> float:
        """Calculate progress as a percentage."""
        if self.total_operations == 0:
            return 0.0
        return ((self.completed_operations + self.failed_operations) / self.total_operations) * 100.0


class OrchestratorMonitor:
    """
    Comprehensive monitoring and logging utility for data orchestrators.
    
    This class provides enhanced logging, progress tracking, execution time monitoring,
    error handling, and summary reporting functionality.
    """
    
    def __init__(self, common_utils: CommonUtils, orchestrator_name: str):
        """
        Initialize the orchestrator monitor.
        
        Args:
            common_utils: CommonUtils instance for logging and utilities
            orchestrator_name: Name of the orchestrator being monitored
        """
        self.common_utils = common_utils
        self.logger = common_utils.log
        self.orchestrator_name = orchestrator_name
        
        # Execution tracking
        self.execution_metrics: Dict[str, ExecutionMetrics] = {}
        self.global_metrics = ExecutionMetrics()
        self.global_start_time = None
        
        # Setup enhanced logging
        self.setup_logging()
    
    def setup_logging(self, log_level: str = "INFO") -> None:
        """
        Set up enhanced logging with appropriate format and level.
        
        Args:
            log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        """
        self.logger.info(f"Initializing {self.orchestrator_name} with enhanced monitoring")
        self.logger.info(f"Logging level: {log_level}")
        self.logger.info(f"Monitor initialized at: {datetime.now(pytz.UTC).isoformat()}")
    
    def start_global_execution(self) -> None:
        """Start tracking global execution time."""
        self.global_start_time = datetime.now(pytz.UTC)
        self.global_metrics.start_time = self.global_start_time
        self.logger.info(f"Starting {self.orchestrator_name} execution at {self.global_start_time.isoformat()}")
    
    def end_global_execution(self) -> None:
        """End tracking global execution time."""
        if self.global_start_time:
            end_time = datetime.now(pytz.UTC)
            self.global_metrics.end_time = end_time
            self.global_metrics.execution_time = (end_time - self.global_start_time).total_seconds()
            self.logger.info(f"Completed {self.orchestrator_name} execution at {end_time.isoformat()}")
            self.logger.info(f"Total execution time: {self.global_metrics.execution_time:.2f} seconds")
    
    def report_progress(self, exchange: str, pair: str, writer_type: str, progress: float) -> None:
        """
        Report progress for a specific operation.
        
        Args:
            exchange: Exchange name
            pair: Trading pair
            writer_type: Writer type
            progress: Progress percentage (0.0 to 1.0)
        """
        progress_percent = progress * 100
        operation_key = f"{exchange}_{pair}_{writer_type}"
        
        self.logger.info(
            f"Progress [{operation_key}]: {progress_percent:.1f}% - "
            f"Exchange: {exchange}, Pair: {pair}, Writer: {writer_type}"
        )
        
        # Update metrics if exists
        if operation_key in self.execution_metrics:
            metrics = self.execution_metrics[operation_key]
            metrics.completed_operations = int(progress * metrics.total_operations)
    
    @contextmanager
    def track_execution_time(self, writer_type: str, exchange: str, pair: str):
        """
        Create a context manager to track execution time.
        
        Args:
            writer_type: Writer type
            exchange: Exchange name
            pair: Trading pair
            
        Yields:
            ExecutionMetrics object for the operation
        """
        operation_key = f"{exchange}_{pair}_{writer_type}"
        
        # Initialize metrics for this operation
        if operation_key not in self.execution_metrics:
            self.execution_metrics[operation_key] = ExecutionMetrics(
                exchange=exchange,
                pair=pair,
                writer_type=writer_type,
                total_operations=1
            )
        
        metrics = self.execution_metrics[operation_key]
        start_time = datetime.now(pytz.UTC)
        metrics.start_time = start_time
        
        self.logger.info(f"Starting {writer_type} for {exchange} {pair} at {start_time.isoformat()}")
        
        try:
            yield metrics
            
            # Mark as successful
            end_time = datetime.now(pytz.UTC)
            metrics.end_time = end_time
            metrics.execution_time = (end_time - start_time).total_seconds()
            metrics.completed_operations += 1
            
            self.logger.success(
                f"Completed {writer_type} for {exchange} {pair} in {metrics.execution_time:.2f} seconds"
            )
            
        except Exception as e:
            # Mark as failed
            end_time = datetime.now(pytz.UTC)
            metrics.end_time = end_time
            metrics.execution_time = (end_time - start_time).total_seconds()
            metrics.failed_operations += 1
            
            # Handle the error
            self.handle_writer_error(e, writer_type, exchange, pair)
            raise
    
    def handle_writer_error(self, error: Exception, writer_type: str, exchange: str, pair: str) -> bool:
        """
        Handle errors from writers with appropriate logging and recovery.
        
        Args:
            error: Exception that occurred
            writer_type: Writer type
            exchange: Exchange name
            pair: Trading pair
            
        Returns:
            True if error was handled and execution can continue, False otherwise
        """
        operation_key = f"{exchange}_{pair}_{writer_type}"
        error_context = {
            "writer_type": writer_type,
            "exchange": exchange,
            "pair": pair,
            "operation_key": operation_key,
            "timestamp": datetime.now(pytz.UTC).isoformat(),
            "error_type": type(error).__name__,
            "error_message": str(error),
            "traceback": traceback.format_exc()
        }
        
        # Log detailed error information
        self.logger.error(
            f"Error in {writer_type} for {exchange} {pair}: {str(error)}"
        )
        self.logger.error(f"Error context: {error_context}")
        
        # Store error in metrics
        if operation_key in self.execution_metrics:
            self.execution_metrics[operation_key].errors.append(error_context)
        
        # Store in global metrics
        self.global_metrics.errors.append(error_context)
        
        # Determine if execution can continue (most writer errors are recoverable)
        recoverable_errors = [
            "ImportError",
            "ModuleNotFoundError", 
            "ConnectionError",
            "TimeoutError",
            "ValueError"
        ]
        
        can_continue = type(error).__name__ in recoverable_errors
        
        if can_continue:
            self.logger.warning(f"Error is recoverable, continuing with next operation")
        else:
            self.logger.error(f"Error is not recoverable, execution may need to stop")
        
        return can_continue
    
    def generate_execution_summary(self) -> Dict[str, Any]:
        """
        Generate a comprehensive summary of the orchestrator execution.
        
        Returns:
            Dictionary with execution summary
        """
        # End global execution if not already ended
        if self.global_metrics.end_time is None:
            self.end_global_execution()
        
        # Calculate global statistics
        total_operations = len(self.execution_metrics)
        successful_operations = sum(1 for m in self.execution_metrics.values() if m.completed_operations > 0)
        failed_operations = sum(1 for m in self.execution_metrics.values() if m.failed_operations > 0)
        total_errors = len(self.global_metrics.errors)
        
        # Calculate timing statistics
        execution_times = [m.execution_time for m in self.execution_metrics.values() if m.execution_time]
        avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0
        min_execution_time = min(execution_times) if execution_times else 0
        max_execution_time = max(execution_times) if execution_times else 0
        
        summary = {
            "orchestrator_name": self.orchestrator_name,
            "execution_summary": {
                "start_time": self.global_metrics.start_time.isoformat() if self.global_metrics.start_time else None,
                "end_time": self.global_metrics.end_time.isoformat() if self.global_metrics.end_time else None,
                "total_execution_time": self.global_metrics.execution_time,
                "total_operations": total_operations,
                "successful_operations": successful_operations,
                "failed_operations": failed_operations,
                "success_rate": (successful_operations / total_operations * 100) if total_operations > 0 else 0,
                "total_errors": total_errors
            },
            "timing_statistics": {
                "average_execution_time": avg_execution_time,
                "minimum_execution_time": min_execution_time,
                "maximum_execution_time": max_execution_time
            },
            "operation_details": {
                key: {
                    "exchange": metrics.exchange,
                    "pair": metrics.pair,
                    "writer_type": metrics.writer_type,
                    "execution_time": metrics.execution_time,
                    "success": metrics.completed_operations > 0,
                    "error_count": len(metrics.errors)
                }
                for key, metrics in self.execution_metrics.items()
            },
            "errors": self.global_metrics.errors[-10:] if self.global_metrics.errors else []  # Last 10 errors
        }
        
        # Log summary
        self.logger.info("=" * 80)
        self.logger.info(f"{self.orchestrator_name.upper()} EXECUTION SUMMARY")
        self.logger.info("=" * 80)
        self.logger.info(f"Total operations: {total_operations}")
        self.logger.info(f"Successful operations: {successful_operations}")
        self.logger.info(f"Failed operations: {failed_operations}")
        self.logger.info(f"Success rate: {summary['execution_summary']['success_rate']:.1f}%")
        self.logger.info(f"Total execution time: {self.global_metrics.execution_time:.2f} seconds")
        self.logger.info(f"Average operation time: {avg_execution_time:.2f} seconds")
        self.logger.info(f"Total errors: {total_errors}")
        self.logger.info("=" * 80)
        
        return summary
