#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Orchestrator Monitoring and Logging Utilities

This module provides comprehensive logging, error handling, progress tracking,
and execution monitoring functionality for data orchestrators.
"""

import time
import traceback
import psutil
import os
from contextlib import contextmanager
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field

import pytz

from framework.utils.common import CommonUtils


@dataclass
class ResourceUsage:
    """Data class to track resource usage metrics."""

    # Memory metrics (in MB)
    memory_start: Optional[float] = None
    memory_end: Optional[float] = None
    memory_peak: Optional[float] = None
    memory_delta: Optional[float] = None

    # CPU metrics
    cpu_percent: Optional[float] = None
    cpu_count: Optional[int] = None

    # Process metrics
    process_id: Optional[int] = None
    thread_count: Optional[int] = None

    # System metrics
    system_memory_total: Optional[float] = None
    system_memory_available: Optional[float] = None
    system_cpu_count: Optional[int] = None


@dataclass
class ExecutionMetrics:
    """Data class to track execution metrics for orchestrator operations."""

    # Timing metrics
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    execution_time: Optional[float] = None

    # Progress metrics
    total_operations: int = 0
    completed_operations: int = 0
    failed_operations: int = 0

    # Operation details
    exchange: Optional[str] = None
    pair: Optional[str] = None
    writer_type: Optional[str] = None

    # Resource usage tracking
    resource_usage: Optional[ResourceUsage] = field(default_factory=lambda: ResourceUsage())

    # Error tracking
    errors: List[Dict[str, Any]] = field(default_factory=list)
    warnings: List[Dict[str, Any]] = field(default_factory=list)
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate as a percentage."""
        if self.total_operations == 0:
            return 0.0
        return (self.completed_operations / self.total_operations) * 100.0
    
    @property
    def progress_percentage(self) -> float:
        """Calculate progress as a percentage."""
        if self.total_operations == 0:
            return 0.0
        return ((self.completed_operations + self.failed_operations) / self.total_operations) * 100.0


class OrchestratorMonitor:
    """
    Comprehensive monitoring and logging utility for data orchestrators.
    
    This class provides enhanced logging, progress tracking, execution time monitoring,
    error handling, and summary reporting functionality.
    """
    
    def __init__(self, common_utils: CommonUtils, orchestrator_name: str):
        """
        Initialize the orchestrator monitor.
        
        Args:
            common_utils: CommonUtils instance for logging and utilities
            orchestrator_name: Name of the orchestrator being monitored
        """
        self.common_utils = common_utils
        self.logger = common_utils.log
        self.orchestrator_name = orchestrator_name
        
        # Execution tracking
        self.execution_metrics: Dict[str, ExecutionMetrics] = {}
        self.global_metrics = ExecutionMetrics()
        self.global_start_time = None
        
        # Initialize process for resource monitoring
        self.process = psutil.Process(os.getpid())
        self.system_info = self._get_system_info()

        # Setup enhanced logging
        self.setup_logging()
    
    def setup_logging(self, log_level: str = "INFO") -> None:
        """
        Set up enhanced logging with appropriate format and level.
        
        Args:
            log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        """
        self.logger.info(f"Initializing {self.orchestrator_name} with enhanced monitoring")
        self.logger.info(f"Logging level: {log_level}")
        self.logger.info(f"Monitor initialized at: {datetime.now(pytz.UTC).isoformat()}")
        self.logger.info(f"System info: {self.system_info}")

    def _get_system_info(self) -> Dict[str, Any]:
        """Get system information for resource monitoring."""
        try:
            memory = psutil.virtual_memory()
            return {
                "cpu_count": psutil.cpu_count(),
                "memory_total_gb": round(memory.total / (1024**3), 2),
                "memory_available_gb": round(memory.available / (1024**3), 2),
                "process_id": os.getpid()
            }
        except Exception as e:
            self.logger.warning(f"Could not get system info: {e}")
            return {}

    def _get_current_resource_usage(self) -> ResourceUsage:
        """Get current resource usage metrics."""
        try:
            # Memory usage in MB
            memory_info = self.process.memory_info()
            memory_mb = round(memory_info.rss / (1024**2), 2)

            # CPU usage
            cpu_percent = self.process.cpu_percent()

            # System memory
            system_memory = psutil.virtual_memory()
            system_memory_total = round(system_memory.total / (1024**3), 2)
            system_memory_available = round(system_memory.available / (1024**3), 2)

            return ResourceUsage(
                memory_start=memory_mb,
                cpu_percent=cpu_percent,
                cpu_count=psutil.cpu_count(),
                process_id=os.getpid(),
                thread_count=self.process.num_threads(),
                system_memory_total=system_memory_total,
                system_memory_available=system_memory_available,
                system_cpu_count=psutil.cpu_count()
            )
        except Exception as e:
            self.logger.warning(f"Could not get resource usage: {e}")
            return ResourceUsage()

    def _update_resource_usage_end(self, resource_usage: ResourceUsage) -> None:
        """Update resource usage with end metrics."""
        try:
            # End memory usage
            memory_info = self.process.memory_info()
            memory_end = round(memory_info.rss / (1024**2), 2)
            resource_usage.memory_end = memory_end

            # Calculate memory delta
            if resource_usage.memory_start is not None:
                resource_usage.memory_delta = round(memory_end - resource_usage.memory_start, 2)

            # Update peak memory if we have it
            try:
                # Try to get peak memory usage (may not be available on all systems)
                peak_memory = round(self.process.memory_info().peak_wset / (1024**2), 2) if hasattr(self.process.memory_info(), 'peak_wset') else memory_end
                resource_usage.memory_peak = peak_memory
            except:
                resource_usage.memory_peak = memory_end

        except Exception as e:
            self.logger.warning(f"Could not update end resource usage: {e}")
    
    def start_global_execution(self) -> None:
        """Start tracking global execution time."""
        self.global_start_time = datetime.now(pytz.UTC)
        self.global_metrics.start_time = self.global_start_time
        self.logger.info(f"Starting {self.orchestrator_name} execution at {self.global_start_time.isoformat()}")
    
    def end_global_execution(self) -> None:
        """End tracking global execution time."""
        if self.global_start_time:
            end_time = datetime.now(pytz.UTC)
            self.global_metrics.end_time = end_time
            self.global_metrics.execution_time = (end_time - self.global_start_time).total_seconds()
            self.logger.info(f"Completed {self.orchestrator_name} execution at {end_time.isoformat()}")
            self.logger.info(f"Total execution time: {self.global_metrics.execution_time:.2f} seconds")
    
    def report_progress(self, exchange: str, pair: str, writer_type: str, progress: float) -> None:
        """
        Report progress for a specific operation.
        
        Args:
            exchange: Exchange name
            pair: Trading pair
            writer_type: Writer type
            progress: Progress percentage (0.0 to 1.0)
        """
        progress_percent = progress * 100
        operation_key = f"{exchange}_{pair}_{writer_type}"
        
        self.logger.info(
            f"Progress [{operation_key}]: {progress_percent:.1f}% - "
            f"Exchange: {exchange}, Pair: {pair}, Writer: {writer_type}"
        )
        
        # Update metrics if exists
        if operation_key in self.execution_metrics:
            metrics = self.execution_metrics[operation_key]
            metrics.completed_operations = int(progress * metrics.total_operations)
    
    @contextmanager
    def track_execution_time(self, writer_type: str, exchange: str, pair: str):
        """
        Create a context manager to track execution time.
        
        Args:
            writer_type: Writer type
            exchange: Exchange name
            pair: Trading pair
            
        Yields:
            ExecutionMetrics object for the operation
        """
        operation_key = f"{exchange}_{pair}_{writer_type}"
        
        # Initialize metrics for this operation
        if operation_key not in self.execution_metrics:
            self.execution_metrics[operation_key] = ExecutionMetrics(
                exchange=exchange,
                pair=pair,
                writer_type=writer_type,
                total_operations=1
            )
        
        metrics = self.execution_metrics[operation_key]
        start_time = datetime.now(pytz.UTC)
        metrics.start_time = start_time

        # Get initial resource usage
        metrics.resource_usage = self._get_current_resource_usage()

        self.logger.info(f"Starting {writer_type} for {exchange} {pair} at {start_time.isoformat()}")
        self.logger.info(f"Initial memory usage: {metrics.resource_usage.memory_start:.2f} MB, CPU: {metrics.resource_usage.cpu_percent:.1f}%")

        try:
            yield metrics

            # Mark as successful
            end_time = datetime.now(pytz.UTC)
            metrics.end_time = end_time
            metrics.execution_time = (end_time - start_time).total_seconds()
            metrics.completed_operations += 1

            # Update resource usage at end
            self._update_resource_usage_end(metrics.resource_usage)

            self.logger.success(
                f"Completed {writer_type} for {exchange} {pair} in {metrics.execution_time:.2f} seconds"
            )
            self.logger.info(
                f"Resource usage - Memory: {metrics.resource_usage.memory_start:.2f} → {metrics.resource_usage.memory_end:.2f} MB "
                f"(Δ{metrics.resource_usage.memory_delta:+.2f} MB), Peak: {metrics.resource_usage.memory_peak:.2f} MB"
            )
            
        except Exception as e:
            # Mark as failed
            end_time = datetime.now(pytz.UTC)
            metrics.end_time = end_time
            metrics.execution_time = (end_time - start_time).total_seconds()
            metrics.failed_operations += 1

            # Update resource usage even on failure
            self._update_resource_usage_end(metrics.resource_usage)

            # Handle the error
            self.handle_writer_error(e, writer_type, exchange, pair)
            raise
    
    def handle_writer_error(self, error: Exception, writer_type: str, exchange: str, pair: str) -> bool:
        """
        Handle errors from writers with appropriate logging and recovery.
        
        Args:
            error: Exception that occurred
            writer_type: Writer type
            exchange: Exchange name
            pair: Trading pair
            
        Returns:
            True if error was handled and execution can continue, False otherwise
        """
        operation_key = f"{exchange}_{pair}_{writer_type}"
        error_context = {
            "writer_type": writer_type,
            "exchange": exchange,
            "pair": pair,
            "operation_key": operation_key,
            "timestamp": datetime.now(pytz.UTC).isoformat(),
            "error_type": type(error).__name__,
            "error_message": str(error),
            "traceback": traceback.format_exc()
        }
        
        # Log detailed error information
        self.logger.error(
            f"Error in {writer_type} for {exchange} {pair}: {str(error)}"
        )
        self.logger.error(f"Error context: {error_context}")
        
        # Store error in metrics
        if operation_key in self.execution_metrics:
            self.execution_metrics[operation_key].errors.append(error_context)
        
        # Store in global metrics
        self.global_metrics.errors.append(error_context)
        
        # Determine if execution can continue (most writer errors are recoverable)
        recoverable_errors = [
            "ImportError",
            "ModuleNotFoundError", 
            "ConnectionError",
            "TimeoutError",
            "ValueError"
        ]
        
        can_continue = type(error).__name__ in recoverable_errors
        
        if can_continue:
            self.logger.warning(f"Error is recoverable, continuing with next operation")
        else:
            self.logger.error(f"Error is not recoverable, execution may need to stop")
        
        return can_continue
    
    def generate_execution_summary(self) -> Dict[str, Any]:
        """
        Generate a comprehensive summary of the orchestrator execution.
        
        Returns:
            Dictionary with execution summary
        """
        # End global execution if not already ended
        if self.global_metrics.end_time is None:
            self.end_global_execution()
        
        # Calculate global statistics
        total_operations = len(self.execution_metrics)
        successful_operations = sum(1 for m in self.execution_metrics.values() if m.completed_operations > 0)
        failed_operations = sum(1 for m in self.execution_metrics.values() if m.failed_operations > 0)
        total_errors = len(self.global_metrics.errors)
        
        # Calculate timing statistics
        execution_times = [m.execution_time for m in self.execution_metrics.values() if m.execution_time]
        avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0
        min_execution_time = min(execution_times) if execution_times else 0
        max_execution_time = max(execution_times) if execution_times else 0

        # Calculate resource usage statistics
        memory_deltas = [m.resource_usage.memory_delta for m in self.execution_metrics.values()
                        if m.resource_usage and m.resource_usage.memory_delta is not None]
        memory_peaks = [m.resource_usage.memory_peak for m in self.execution_metrics.values()
                       if m.resource_usage and m.resource_usage.memory_peak is not None]

        resource_stats = {
            "memory_delta_avg": round(sum(memory_deltas) / len(memory_deltas), 2) if memory_deltas else 0,
            "memory_delta_max": round(max(memory_deltas), 2) if memory_deltas else 0,
            "memory_delta_min": round(min(memory_deltas), 2) if memory_deltas else 0,
            "memory_peak_max": round(max(memory_peaks), 2) if memory_peaks else 0,
            "memory_peak_avg": round(sum(memory_peaks) / len(memory_peaks), 2) if memory_peaks else 0,
            "system_info": self.system_info
        }
        
        summary = {
            "orchestrator_name": self.orchestrator_name,
            "execution_summary": {
                "start_time": self.global_metrics.start_time.isoformat() if self.global_metrics.start_time else None,
                "end_time": self.global_metrics.end_time.isoformat() if self.global_metrics.end_time else None,
                "total_execution_time": self.global_metrics.execution_time,
                "total_operations": total_operations,
                "successful_operations": successful_operations,
                "failed_operations": failed_operations,
                "success_rate": (successful_operations / total_operations * 100) if total_operations > 0 else 0,
                "total_errors": total_errors
            },
            "timing_statistics": {
                "average_execution_time": avg_execution_time,
                "minimum_execution_time": min_execution_time,
                "maximum_execution_time": max_execution_time
            },
            "resource_statistics": resource_stats,
            "operation_details": {
                key: {
                    "exchange": metrics.exchange,
                    "pair": metrics.pair,
                    "writer_type": metrics.writer_type,
                    "execution_time": metrics.execution_time,
                    "success": metrics.completed_operations > 0,
                    "error_count": len(metrics.errors),
                    "resource_usage": {
                        "memory_start": metrics.resource_usage.memory_start if metrics.resource_usage else None,
                        "memory_end": metrics.resource_usage.memory_end if metrics.resource_usage else None,
                        "memory_delta": metrics.resource_usage.memory_delta if metrics.resource_usage else None,
                        "memory_peak": metrics.resource_usage.memory_peak if metrics.resource_usage else None,
                        "cpu_percent": metrics.resource_usage.cpu_percent if metrics.resource_usage else None
                    }
                }
                for key, metrics in self.execution_metrics.items()
            },
            "errors": self.global_metrics.errors[-10:] if self.global_metrics.errors else []  # Last 10 errors
        }
        
        # Log summary
        self.logger.info("=" * 80)
        self.logger.info(f"{self.orchestrator_name.upper()} EXECUTION SUMMARY")
        self.logger.info("=" * 80)
        self.logger.info(f"Total operations: {total_operations}")
        self.logger.info(f"Successful operations: {successful_operations}")
        self.logger.info(f"Failed operations: {failed_operations}")
        self.logger.info(f"Success rate: {summary['execution_summary']['success_rate']:.1f}%")
        if self.global_metrics.execution_time is not None:
            self.logger.info(f"Total execution time: {self.global_metrics.execution_time:.2f} seconds")
        else:
            self.logger.info("Total execution time: Not available")
        self.logger.info(f"Average operation time: {avg_execution_time:.2f} seconds")
        self.logger.info(f"Total errors: {total_errors}")

        # Log resource usage statistics
        if resource_stats["memory_delta_avg"] != 0 or resource_stats["memory_peak_max"] != 0:
            self.logger.info("--- Resource Usage Statistics ---")
            self.logger.info(f"Memory delta - Avg: {resource_stats['memory_delta_avg']:+.2f} MB, "
                           f"Max: {resource_stats['memory_delta_max']:+.2f} MB, "
                           f"Min: {resource_stats['memory_delta_min']:+.2f} MB")
            self.logger.info(f"Memory peak - Max: {resource_stats['memory_peak_max']:.2f} MB, "
                           f"Avg: {resource_stats['memory_peak_avg']:.2f} MB")
            if self.system_info:
                self.logger.info(f"System - CPU cores: {self.system_info.get('cpu_count', 'N/A')}, "
                               f"Total RAM: {self.system_info.get('memory_total_gb', 'N/A')} GB")

        self.logger.info("=" * 80)
        
        return summary
