#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Refactored Futures Pair Data Orchestrator

This module provides a refactored version of the futures pair data orchestrator
using the new utility modules for better code organization and reduced duplication.
"""

import argparse
import json
import sys
from typing import Dict, List, Any, Optional

from framework.utils.common import CommonUtils
from .utils.base_orchestrator import BaseOrchestrator
from .utils.config_manager import ConfigManager


class RefactoredFuturesPairDataOrchestrator(BaseOrchestrator):
    """
    Refactored orchestrator for futures market data backfill using utility modules.
    
    This class manages futures-specific data writers, provides checkpointing and recovery
    capabilities, and supports parallel processing using the new utility framework.
    """
    
    # Define futures writer types
    WRITER_TYPES = [
        "funding_data_writer",
        "liquidations_data_writer", 
        "open_interest_data_writer",
        "long_short_data_writer"
    ]
    
    # Futures writers are mostly independent, so no complex dependencies
    WRITER_DEPENDENCIES = {
        "funding_data_writer": [],
        "liquidations_data_writer": [],
        "open_interest_data_writer": [],
        "long_short_data_writer": []
    }
    
    def __init__(self, config: Dict[str, Any], common_utils: CommonUtils):
        """
        Initialize the refactored futures orchestrator.
        
        Args:
            config: Configuration dictionary
            common_utils: CommonUtils instance for logging and utilities
        """
        super().__init__(config, common_utils, "futures")
        
        # Validate writer dependencies
        self._validate_writer_dependencies()
    
    def _validate_writer_dependencies(self) -> None:
        """Validate that all writer dependencies are properly defined."""
        for writer in self.WRITER_TYPES:
            if writer not in self.WRITER_DEPENDENCIES:
                raise ValueError(f"Writer {writer} is missing dependency definition")
        
        self.logger.info(f"Validated dependencies for {len(self.WRITER_TYPES)} futures writers")
    
    def get_writer_types(self) -> List[str]:
        """Get list of all writer types supported by this orchestrator."""
        return self.WRITER_TYPES.copy()
    
    def _get_exchange_formatter(self) -> Optional[callable]:
        """Get exchange-specific formatter for futures markets."""
        def futures_formatter(exchange: str, pair: str) -> Dict[str, Any]:
            format_result = self.exchange_handler.format_futures_pair(exchange, pair)
            return {
                "pair": format_result.pair,
                "contract_type": format_result.contract_type
            }
        
        return futures_formatter
    
    def check_dependencies(self, writer_type: str, exchange: str, pair: str) -> bool:
        """
        Check if dependencies for a writer are satisfied.
        
        Args:
            writer_type: Writer type to check
            exchange: Exchange name
            pair: Trading pair
            
        Returns:
            True if all dependencies are satisfied (always True for futures writers)
        """
        # Futures writers are independent, so dependencies are always satisfied
        dependencies = self.WRITER_DEPENDENCIES.get(writer_type, [])
        
        for dependency in dependencies:
            checkpoint = self.checkpoint_manager.load_checkpoint(exchange, pair, dependency)
            if not checkpoint:
                self.logger.warning(f"Dependency {dependency} not satisfied for {writer_type} on {exchange} {pair}")
                return False
        
        return True
    
    def run_writer(self, writer_type: str, exchange: str, pair: str, 
                   start_date: str, end_date: str) -> bool:
        """
        Run a specific futures writer for a specific exchange and pair.
        
        Args:
            writer_type: Writer type to run
            exchange: Exchange name
            pair: Trading pair
            start_date: Start date for execution
            end_date: End date for execution
            
        Returns:
            True if execution was successful, False otherwise
        """
        if writer_type not in self.WRITER_TYPES:
            self.logger.error(f"Unknown writer type: {writer_type}")
            return False
        
        # Check dependencies (always satisfied for futures)
        if not self.check_dependencies(writer_type, exchange, pair):
            self.logger.error(f"Dependencies not satisfied for {writer_type} on {exchange} {pair}")
            return False
        
        return self.execute_writer_for_pair(writer_type, exchange, pair, start_date, end_date)
    
    def run_all_writers_for_exchange(self, exchange: str, pairs: List[str]) -> Dict[str, Dict[str, bool]]:
        """
        Run all writers for a specific exchange.
        
        Args:
            exchange: Exchange name
            pairs: List of trading pairs
            
        Returns:
            Dictionary with results for each writer and pair
        """
        self.logger.info(f"Running all futures writers for {exchange} with {len(pairs)} pairs")
        
        if self.config.get("parallel", False):
            # Run all writers in parallel for this exchange
            return self.execute_writers_parallel(
                self.WRITER_TYPES, 
                exchange, 
                pairs, 
                self.config["start_date"], 
                self.config["end_date"]
            )
        else:
            # Run all writers sequentially for this exchange
            results = {}
            for pair in pairs:
                pair_results = {}
                for writer_type in self.WRITER_TYPES:
                    self.logger.info(f"Running {writer_type} for {exchange} {pair}")
                    result = self.run_writer(
                        writer_type, 
                        exchange, 
                        pair, 
                        self.config["start_date"], 
                        self.config["end_date"]
                    )
                    pair_results[writer_type] = result
                
                # Store results for this pair
                if exchange not in results:
                    results[exchange] = {}
                results[exchange][pair] = pair_results
            
            return results
    
    def run_all_operations(self) -> Dict[str, Any]:
        """
        Run all writers for all exchanges and pairs.
        
        Returns:
            Dictionary with results for each exchange, pair, and writer
        """
        self.logger.info("Running all futures writers for all exchanges and pairs")
        
        results = {}
        
        for exchange, pairs in self.config["pairs"].items():
            self.logger.info(f"Processing exchange: {exchange}")
            
            # Validate exchange support for futures
            if not self.exchange_handler.validate_exchange_support(exchange, "futures"):
                self.logger.warning(f"Exchange {exchange} may not support futures trading")
            
            # Normalize pairs for this exchange
            normalized_pairs = self.exchange_handler.normalize_pair_list(exchange, pairs, "futures")
            
            # Log any formatting warnings
            for original_pair, format_result in normalized_pairs.items():
                for warning in format_result.warnings:
                    self.logger.warning(warning)
            
            # Use formatted pairs for execution
            formatted_pairs = [result.pair for result in normalized_pairs.values()]
            
            # Run all writers for this exchange
            exchange_results = self.run_all_writers_for_exchange(exchange, formatted_pairs)
            results[exchange] = exchange_results.get(exchange, exchange_results)
        
        return results
    
    def run_specific_writer_all_exchanges(self, writer_type: str) -> Dict[str, Dict[str, bool]]:
        """
        Run a specific writer for all exchanges and pairs.
        
        Args:
            writer_type: Writer type to run
            
        Returns:
            Dictionary with results for each exchange and pair
        """
        if writer_type not in self.WRITER_TYPES:
            raise ValueError(f"Unknown writer type: {writer_type}")
        
        self.logger.info(f"Running {writer_type} for all exchanges and pairs")
        
        results = {}
        
        for exchange, pairs in self.config["pairs"].items():
            exchange_results = {}
            
            if self.config.get("parallel", False):
                # Run pairs in parallel for this writer and exchange
                pair_results = self.parallel_executor.execute_pairs_parallel(
                    pairs, exchange, writer_type, 
                    self.config["start_date"], self.config["end_date"],
                    self.run_writer
                )
                exchange_results = pair_results
            else:
                # Run pairs sequentially
                for pair in pairs:
                    result = self.run_writer(
                        writer_type, exchange, pair, 
                        self.config["start_date"], self.config["end_date"]
                    )
                    exchange_results[pair] = result
            
            results[exchange] = exchange_results
        
        return results
    
    def get_futures_market_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the futures market configuration.
        
        Returns:
            Dictionary with market summary information
        """
        total_pairs = sum(len(pairs) for pairs in self.config["pairs"].values())
        
        summary = {
            "total_exchanges": len(self.config["pairs"]),
            "total_pairs": total_pairs,
            "writer_types": self.WRITER_TYPES.copy(),
            "total_operations": total_pairs * len(self.WRITER_TYPES),
            "parallel_enabled": self.config.get("parallel", False),
            "max_workers": self.config.get("max_workers", 10),
            "exchanges": {}
        }
        
        for exchange, pairs in self.config["pairs"].items():
            exchange_info = self.exchange_handler.get_exchange_info(exchange)
            summary["exchanges"][exchange] = {
                "pair_count": len(pairs),
                "pairs": pairs,
                "supports_futures": exchange_info.get("supports_futures", True),
                "pair_format": exchange_info.get("futures_pair_format", "unknown")
            }
        
        return summary


def main(argv: List[str] = None) -> None:
    """
    Main function for the refactored futures orchestrator.
    
    Args:
        argv: Command line arguments
    """
    parser = argparse.ArgumentParser(description="Refactored Futures Pair Data Orchestrator")
    parser.add_argument("-c", "--config", required=True, help="Configuration file path")
    parser.add_argument("-w", "--writer", help="Specific writer to run")
    parser.add_argument("-e", "--exchange", help="Specific exchange to run")
    parser.add_argument("-v", "--verbose", action="store_true", help="Enable verbose logging")
    parser.add_argument("--summary", action="store_true", help="Show market summary")
    
    args = parser.parse_args(argv)
    
    # Initialize CommonUtils
    log_level = "DEBUG" if args.verbose else "INFO"
    common_utils = CommonUtils(name="refactored_futures_orchestrator", log_level=log_level)
    logger = common_utils.log
    
    try:
        # Load configuration
        config_manager = ConfigManager(common_utils)
        config = config_manager.load_config_from_file(args.config)
        
        # Initialize orchestrator
        orchestrator = RefactoredFuturesPairDataOrchestrator(config, common_utils)
        
        # Show summary if requested
        if args.summary:
            summary = orchestrator.get_futures_market_summary()
            logger.info("Futures Market Summary:")
            logger.info(json.dumps(summary, indent=2))
            return
        
        # Run orchestrator
        if args.writer:
            # Run specific writer with monitoring
            orchestrator.monitor.start_global_execution()
            results = orchestrator.run_specific_writer_all_exchanges(args.writer)
            orchestrator.monitor.end_global_execution()
            summary = orchestrator.monitor.generate_execution_summary()
            results["execution_summary"] = summary
        else:
            # Run all operations with monitoring
            results = orchestrator.run_all_with_monitoring()
        
        logger.info("Orchestrator run complete")
        
        # Log results without the execution summary for cleaner output
        results_without_summary = {k: v for k, v in results.items() if k != "execution_summary"}
        logger.info(f"Results: {json.dumps(results_without_summary, indent=2)}")
        
    except Exception as e:
        logger.error(f"Error running orchestrator: {str(e)}")
        sys.exit(1)
    finally:
        common_utils.finalize()


if __name__ == "__main__":
    main()
