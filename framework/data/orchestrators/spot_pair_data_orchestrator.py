#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Spot Pair Data Orchestrator

This module provides a framework for orchestrating the backfill process for spot market data
across multiple exchanges and trading pairs. It manages dependencies between different data types,
provides checkpointing and recovery capabilities, and supports parallel processing where appropriate.
"""

import os
import sys
import json
import csv
import importlib
import hashlib
import concurrent.futures
from typing import Dict, List, Optional, Any
import logging
from datetime import datetime
from pathlib import Path

import pytz

from framework.utils.common import CommonUtils
from framework.data.orchestrators.utils.orchestrator_monitoring import OrchestratorMonitor


class SpotPairDataOrchestrator:
    """
    Orchestrates the backfill process for spot market data across multiple exchanges and trading pairs.
    
    This class manages dependencies between different data types, provides checkpointing and recovery
    capabilities, and supports parallel processing where appropriate.
    """
    
    # Define writer levels and dependencies
    WRITER_LEVELS = {
        "level_1": ["tick_data_writer"],
        "level_2": ["price_derived_data_writer", "volume_data_writer", "orderbook_data_writer"],
        "level_3": ["volatility_data_writer"],
        "level_4": ["correlation_data_writer"],
        "level_5": ["price_market_index_data_writer"]
    }
    
    WRITER_DEPENDENCIES = {
        "tick_data_writer": [],
        "price_derived_data_writer": ["tick_data_writer"],
        "volume_data_writer": ["tick_data_writer"],
        "orderbook_data_writer": ["tick_data_writer"],
        "volatility_data_writer": ["price_derived_data_writer"],
        "correlation_data_writer": ["price_derived_data_writer"],
        "price_market_index_data_writer": ["price_derived_data_writer"]
    }
    
    def __init__(self, config: Dict, common_utils: CommonUtils):
        """
        Initialize the orchestrator with configuration and utilities.

        Args:
            config: Configuration dictionary for the orchestrator
            common_utils: CommonUtils instance for logging and other utilities
        """
        self.config = config
        self.common_utils = common_utils
        self.logger = common_utils.log

        # Initialize monitoring and enhanced logging
        self.monitor = OrchestratorMonitor(common_utils, "SpotPairDataOrchestrator")
        
        # Set up checkpoint directory
        self.checkpoint_dir = os.path.join(
            os.path.dirname(os.path.abspath(__file__)), 
            "..", "..", "..", "data", "checkpoints", "spot"
        )
        os.makedirs(self.checkpoint_dir, exist_ok=True)
        
        # Validate configuration
        self._validate_config()
        
        # Initialize writer modules
        self.writer_modules = {}
        
    def _validate_config(self) -> None:
        """
        Validate the configuration dictionary.
        
        Raises:
            ValueError: If the configuration is invalid
        """
        required_keys = ["start_date", "end_date", "pairs"]
        for key in required_keys:
            if key not in self.config:
                raise ValueError(f"Missing required configuration key: {key}")
        
        # Validate date formats
        for date_key in ["start_date", "end_date"]:
            try:
                datetime.strptime(self.config[date_key], "%Y-%m-%d-%H:%M:%S")
            except ValueError:
                raise ValueError(f"Invalid date format for {date_key}: {self.config[date_key]}, expected YYYY-MM-DD-HH:MM:SS")
        
        # Validate pairs configuration
        if not isinstance(self.config["pairs"], dict):
            raise ValueError("Pairs configuration must be a dictionary mapping exchanges to lists of pairs")
        
        for exchange, pairs in self.config["pairs"].items():
            if not isinstance(pairs, list):
                raise ValueError(f"Pairs for exchange {exchange} must be a list")
    
    def _generate_checkpoint_key(self, exchange: str, pair: str, writer_type: str) -> str:
        """
        Generate a unique key for a checkpoint.
        
        Args:
            exchange: Exchange name
            pair: Trading pair
            writer_type: Writer type
            
        Returns:
            Unique checkpoint key
        """
        return f"{exchange}_{pair}_{writer_type}"
    
    def _generate_config_hash(self) -> str:
        """
        Generate a hash of the configuration for checkpoint validation.
        
        Returns:
            Hash string of the configuration
        """
        config_str = json.dumps(self.config, sort_keys=True)
        return hashlib.md5(config_str.encode()).hexdigest()
    
    def _get_checkpoint_path(self, exchange: str, pair: str, writer_type: str) -> str:
        """
        Get the path to a checkpoint file.
        
        Args:
            exchange: Exchange name
            pair: Trading pair
            writer_type: Writer type
            
        Returns:
            Path to checkpoint file
        """
        key = self._generate_checkpoint_key(exchange, pair, writer_type)
        return os.path.join(self.checkpoint_dir, f"{key}.json")
    
    def save_checkpoint(self, exchange: str, pair: str, writer_type: str, last_date: str) -> None:
        """
        Save a checkpoint for a writer execution.
        
        Args:
            exchange: Exchange name
            pair: Trading pair
            writer_type: Writer type
            last_date: Last processed date
        """
        checkpoint_path = self._get_checkpoint_path(exchange, pair, writer_type)
        
        checkpoint_data = {
            "exchange": exchange,
            "pair": pair,
            "writer_type": writer_type,
            "last_date": last_date,
            "config_hash": self._generate_config_hash(),
            "timestamp": datetime.now(pytz.UTC).strftime("%Y-%m-%d-%H:%M:%S")
        }
        
        # Save as JSON
        with open(checkpoint_path, 'w') as f:
            json.dump(checkpoint_data, f, indent=2)
        
        self.logger.info(f"Saved checkpoint for {exchange} {pair} {writer_type} at {last_date}")
    
    def load_checkpoint(self, exchange: str, pair: str, writer_type: str) -> Optional[Dict]:
        """
        Load a checkpoint for a writer execution.
        
        Args:
            exchange: Exchange name
            pair: Trading pair
            writer_type: Writer type
            
        Returns:
            Checkpoint data or None if no checkpoint exists
        """
        checkpoint_path = self._get_checkpoint_path(exchange, pair, writer_type)
        
        if not os.path.exists(checkpoint_path):
            return None
        
        try:
            with open(checkpoint_path, 'r') as f:
                checkpoint_data = json.load(f)
            
            # Validate config hash
            if checkpoint_data.get("config_hash") != self._generate_config_hash():
                self.logger.warning(f"Checkpoint config hash mismatch for {exchange} {pair} {writer_type}, ignoring checkpoint")
                return None
            
            self.logger.info(f"Loaded checkpoint for {exchange} {pair} {writer_type} at {checkpoint_data.get('last_date')}")
            return checkpoint_data
        except Exception as e:
            self.logger.error(f"Error loading checkpoint for {exchange} {pair} {writer_type}: {str(e)}")
            return None
    
    def check_dependencies(self, writer_type: str, exchange: str, pair: str) -> bool:
        """
        Check if dependencies for a writer are satisfied.
        
        Args:
            writer_type: Writer type
            exchange: Exchange name
            pair: Trading pair
            
        Returns:
            True if dependencies are satisfied, False otherwise
        """
        dependencies = self.WRITER_DEPENDENCIES.get(writer_type, [])
        
        if not dependencies:
            return True
        
        for dependency in dependencies:
            checkpoint = self.load_checkpoint(exchange, pair, dependency)
            if not checkpoint:
                self.logger.warning(f"Dependency {dependency} not satisfied for {writer_type} {exchange} {pair}")
                return False
        
        return True
    
    def run_writer(self, writer_type: str, exchange: str, pair: str, start_date: str, end_date: str) -> bool:
        """
        Run a writer for a specific exchange and pair.
        
        Args:
            writer_type: Writer type
            exchange: Exchange name
            pair: Trading pair
            start_date: Start date
            end_date: End date
            
        Returns:
            True if successful, False otherwise
        """
        self.logger.info(f"Running {writer_type} for {exchange} {pair} from {start_date} to {end_date}")
        
        # Check dependencies
        if not self.check_dependencies(writer_type, exchange, pair):
            self.logger.error(f"Dependencies not satisfied for {writer_type} {exchange} {pair}")
            return False
        
        # Load checkpoint
        checkpoint = self.load_checkpoint(exchange, pair, writer_type)
        if checkpoint:
            start_date = checkpoint.get("last_date")
            self.logger.info(f"Resuming {writer_type} for {exchange} {pair} from {start_date}")
        
        # Import writer module
        if writer_type not in self.writer_modules:
            try:
                self.writer_modules[writer_type] = importlib.import_module(f"framework.data.writers.{writer_type}")
            except ImportError as e:
                self.logger.error(f"Error importing writer module {writer_type}: {str(e)}")
                return False
        
        # Run writer with enhanced monitoring
        try:
            with self.monitor.track_execution_time(writer_type, exchange, pair) as metrics:
                writer_main = getattr(self.writer_modules[writer_type], "main")

                # Report progress start
                self.monitor.report_progress(exchange, pair, writer_type, 0.0)

                # Prepare arguments
                argv = [
                    "-s", start_date,
                    "-e", end_date,
                    "-i", pair,
                    "-x", exchange
                ]

                # Add parallel flag if configured
                if self.config.get("parallel", False):
                    argv.append("-p")

                # Report progress mid-way
                self.monitor.report_progress(exchange, pair, writer_type, 0.5)

                # Run writer
                result = writer_main(argv)

                # Report progress completion
                self.monitor.report_progress(exchange, pair, writer_type, 1.0)

                # Save checkpoint
                self.save_checkpoint(exchange, pair, writer_type, end_date)

                return True
        except Exception as e:
            # Enhanced error handling
            can_continue = self.monitor.handle_writer_error(e, writer_type, exchange, pair)
            return False
            
    def run_correlation_data_writer(self, exchange: str, pair: str, start_date: str, end_date: str, timeframe: str = "1d") -> bool:
        """
        Execute the correlation data writer for a specific exchange, pair and timeframe.
        
        Args:
            exchange: Exchange name (e.g., 'binance')
            pair: Trading pair (e.g., 'btc_usdt')
            start_date: Start date in format 'YYYY-MM-DD-HH:MM:SS'
            end_date: End date in format 'YYYY-MM-DD-HH:MM:SS'
            timeframe: Correlation timeframe ('1h', '12h', '1d')
            
        Returns:
            True if successful, False otherwise
        """
        writer_type = "correlation_data_writer"
        self.logger.info(f"Running {writer_type} for {exchange} {pair} with timeframe {timeframe} from {start_date} to {end_date}")
        
        # Check dependencies
        if not self.check_dependencies(writer_type, exchange, pair):
            self.logger.error(f"Dependencies not satisfied for {writer_type} {exchange} {pair}")
            return False
        
        # Load checkpoint
        checkpoint_key = f"{writer_type}_{timeframe}"
        checkpoint = self.load_checkpoint(exchange, pair, checkpoint_key)
        if checkpoint:
            start_date = checkpoint.get("last_date")
            self.logger.info(f"Resuming {writer_type} for {exchange} {pair} with timeframe {timeframe} from {start_date}")
        
        # Import writer module
        if writer_type not in self.writer_modules:
            try:
                self.writer_modules[writer_type] = importlib.import_module(f"framework.data.writers.{writer_type}")
            except ImportError as e:
                self.logger.error(f"Error importing writer module {writer_type}: {str(e)}")
                return False
        
        # Run writer with enhanced monitoring
        try:
            with self.monitor.track_execution_time(f"{writer_type}_{timeframe}", exchange, pair) as metrics:
                writer_main = getattr(self.writer_modules[writer_type], "main")

                # Report progress start
                self.monitor.report_progress(exchange, pair, f"{writer_type}_{timeframe}", 0.0)

                # For correlation writer, we need to pass pairs as a list and specify timeframes
                # The correlation writer expects -p for pairs instead of -i
                argv = [
                    "-s", start_date,
                    "-e", end_date,
                    "-p", pair,
                    "-x", exchange,
                    "-t", timeframe,  # Step size (timeframe)
                    "-l", timeframe   # Lookback period (same as timeframe for simplicity)
                ]

                # Add parallel flag if configured
                if self.config.get("parallel", False):
                    argv.append("-P")

                # Report progress mid-way
                self.monitor.report_progress(exchange, pair, f"{writer_type}_{timeframe}", 0.5)

                # Run writer
                result = writer_main(argv)

                # Report progress completion
                self.monitor.report_progress(exchange, pair, f"{writer_type}_{timeframe}", 1.0)

                # Save checkpoint with timeframe in the key
                self.save_checkpoint(exchange, pair, checkpoint_key, end_date)

                return True
        except Exception as e:
            # Enhanced error handling
            can_continue = self.monitor.handle_writer_error(e, f"{writer_type}_{timeframe}", exchange, pair)
            return False
            
    def run_price_market_index_writer(self, exchange: str, pairs: list, start_date: str, end_date: str) -> bool:
        """
        Execute the price market index data writer for a specific exchange and set of pairs.
        
        Args:
            exchange: Exchange name (e.g., 'binance')
            pairs: List of trading pairs
            start_date: Start date in format 'YYYY-MM-DD-HH:MM:SS'
            end_date: End date in format 'YYYY-MM-DD-HH:MM:SS'
            
        Returns:
            True if successful, False otherwise
        """
        writer_type = "price_market_index_data_writer"
        pairs_str = ",".join(pairs)
        self.logger.info(f"Running {writer_type} for {exchange} with pairs {pairs_str} from {start_date} to {end_date}")
        
        # Check dependencies for all pairs
        for pair in pairs:
            if not self.check_dependencies("price_derived_data_writer", exchange, pair):
                self.logger.error(f"Dependencies not satisfied for {writer_type} {exchange} {pair}")
                return False
        
        # Load checkpoint - use a special key for market index since it processes multiple pairs
        checkpoint_key = f"{writer_type}_{exchange}"
        checkpoint = self.load_checkpoint(exchange, "market_index", checkpoint_key)
        if checkpoint:
            start_date = checkpoint.get("last_date")
            self.logger.info(f"Resuming {writer_type} for {exchange} from {start_date}")
        
        # Import writer module
        if writer_type not in self.writer_modules:
            try:
                self.writer_modules[writer_type] = importlib.import_module(f"framework.data.writers.{writer_type}")
            except ImportError as e:
                self.logger.error(f"Error importing writer module {writer_type}: {str(e)}")
                return False
        
        # Run writer
        try:
            writer_main = getattr(self.writer_modules[writer_type], "main")
            
            # The market index writer expects a list of pairs
            argv = [
                "-s", start_date,
                "-e", end_date,
                "-p", pairs_str,
                "-x", exchange
            ]
            
            # Add parallel flag if configured
            if self.config.get("parallel", False):
                argv.append("-p")
            
            # Run writer
            result = writer_main(argv)
            
            # Save checkpoint
            self.save_checkpoint(exchange, "market_index", checkpoint_key, end_date)
            
            return True
        except Exception as e:
            self.logger.error(f"Error running {writer_type} for {exchange}: {str(e)}")
            return False

    def run_writers_in_parallel(self, level: str, exchange: str, pairs: List[str]) -> Dict:
        """
        Execute all writers for a specific level in parallel for a given exchange and pairs.
        
        Args:
            level: Level to run
            exchange: Exchange name
            pairs: List of trading pairs
            
        Returns:
            Dictionary with results for each writer and pair
        """
        self.logger.info(f"Running writers for level {level} in parallel for {exchange}")
        
        writers = self.WRITER_LEVELS.get(level, [])
        results = {}
        
        for writer_type in writers:
            writer_results = {}
            
            if writer_type == "correlation_data_writer":
                for pair in pairs:
                    for timeframe in ["1h", "12h", "1d"]:
                        writer_results[f"{pair}_{timeframe}"] = self.run_correlation_data_writer(exchange, pair, self.config["start_date"], self.config["end_date"], timeframe)
            else:
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future_to_pair = {
                        executor.submit(
                            self.run_writer, 
                            writer_type, 
                            exchange, 
                            pair, 
                            self.config["start_date"], 
                            self.config["end_date"]
                        ): pair
                        for pair in pairs
                    }
                    
                    for future in concurrent.futures.as_completed(future_to_pair):
                        pair = future_to_pair[future]
                        try:
                            result = future.result()
                            writer_results[pair] = result
                        except Exception as e:
                            self.logger.error(f"Error running {writer_type} for {exchange} {pair}: {str(e)}")
                            writer_results[pair] = False
            
            results[writer_type] = writer_results
        
        return results
    
    def run_level(self, level: str) -> Dict:
        """
        Run all writers for a specific level.
        
        Args:
            level: Level to run (level_1, level_2, etc.)
            
        Returns:
            Dictionary with results for each writer
        """
        self.logger.info(f"Running level {level}")
        
        writers = self.WRITER_LEVELS.get(level, [])
        results = {}
        
        for writer_type in writers:
            writer_results = {}
            
            # Special handling for price_market_index_data_writer
            if writer_type == "price_market_index_data_writer":
                for exchange, pairs in self.config["pairs"].items():
                    writer_results[exchange] = self.run_price_market_index_writer(exchange, pairs, self.config["start_date"], self.config["end_date"])
            # Special handling for correlation_data_writer
            elif writer_type == "correlation_data_writer":
                for exchange, pairs in self.config["pairs"].items():
                    exchange_results = {}
                    
                    # Check if parallel execution is enabled
                    if self.config.get("parallel", False):
                        # Run writers in parallel for this exchange and its pairs
                        exchange_results = self.run_writers_in_parallel(level, exchange, pairs)
                        # Extract results for this specific writer
                        if writer_type in exchange_results:
                            writer_results[exchange] = exchange_results[writer_type]
                    else:
                        # Run correlation writer for each pair and timeframe
                        for pair in pairs:
                            pair_results = {}
                            for timeframe in ["1h", "12h", "1d"]:
                                result = self.run_correlation_data_writer(
                                    exchange, 
                                    pair, 
                                    self.config["start_date"], 
                                    self.config["end_date"],
                                    timeframe
                                )
                                pair_results[timeframe] = result
                            exchange_results[pair] = pair_results
                        
                        writer_results[exchange] = exchange_results
            else:
                for exchange, pairs in self.config["pairs"].items():
                    # Check if parallel execution is enabled
                    if self.config.get("parallel", False):
                        # Run writers in parallel for this exchange and its pairs
                        exchange_results = self.run_writers_in_parallel(level, exchange, pairs)
                        # Extract results for this specific writer
                        if writer_type in exchange_results:
                            writer_results[exchange] = exchange_results[writer_type]
                    else:
                        exchange_results = {}
                        
                        for pair in pairs:
                            start_date = self.config["start_date"]
                            end_date = self.config["end_date"]
                            
                            result = self.run_writer(writer_type, exchange, pair, start_date, end_date)
                            exchange_results[pair] = result
                        
                        writer_results[exchange] = exchange_results
            
            results[writer_type] = writer_results
        
        return results
    
    def run_all(self) -> Dict:
        """
        Run all writers for all levels.

        Returns:
            Dictionary with results for each level
        """
        # Start global execution tracking
        self.monitor.start_global_execution()

        self.logger.info("Running all levels")

        results = {}

        try:
            for level in self.WRITER_LEVELS.keys():
                level_results = self.run_level(level)
                results[level] = level_results
        finally:
            # End global execution tracking and generate summary
            self.monitor.end_global_execution()
            summary = self.monitor.generate_execution_summary()

            # Add summary to results
            results["execution_summary"] = summary

        return results


def main():
    """
    Main function for running the spot pair data orchestrator.
    """
    import argparse
    
    parser = argparse.ArgumentParser(description="Spot Pair Data Orchestrator")
    parser.add_argument("-c", "--config", required=True, help="Path to configuration file")
    parser.add_argument("-l", "--level", help="Level to run (level_1, level_2, etc.)")
    parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    # Set up logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    common_utils = CommonUtils(log_level=log_level)
    logger = common_utils.log
    
    # Load configuration
    try:
        with open(args.config, 'r') as f:
            config = json.load(f)
    except Exception as e:
        logger.error(f"Error loading configuration: {str(e)}")
        sys.exit(1)
    
    # Create orchestrator
    orchestrator = SpotPairDataOrchestrator(config, common_utils)
    
    # Run orchestrator
    try:
        if args.level:
            # For single level runs, also track execution
            orchestrator.monitor.start_global_execution()
            results = orchestrator.run_level(args.level)
            orchestrator.monitor.end_global_execution()
            summary = orchestrator.monitor.generate_execution_summary()
            results["execution_summary"] = summary
        else:
            results = orchestrator.run_all()

        logger.info("Orchestrator run complete")

        # Log results without the execution summary for cleaner output
        results_without_summary = {k: v for k, v in results.items() if k != "execution_summary"}
        logger.info(f"Results: {json.dumps(results_without_summary, indent=2)}")

        # The execution summary is already logged by the monitor

    except Exception as e:
        logger.error(f"Error running orchestrator: {str(e)}")
        # Try to generate a final summary even on error
        try:
            orchestrator.monitor.end_global_execution()
            orchestrator.monitor.generate_execution_summary()
        except:
            pass
        sys.exit(1)


if __name__ == "__main__":
    main()
