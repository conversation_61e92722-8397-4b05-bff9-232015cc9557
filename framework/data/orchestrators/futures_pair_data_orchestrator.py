#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Futures Pair Data Orchestrator

This module provides a framework for orchestrating the backfill process for futures market data
across multiple exchanges and trading pairs. It manages dependencies between different data types,
provides checkpointing and recovery capabilities, and supports parallel processing where appropriate.
"""

import os
import sys
import json
import csv
import hashlib
import logging
import importlib
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Union, Any
import concurrent.futures
from pathlib import Path
import re

import pytz

from framework.utils.common import CommonUtils
from framework.data.orchestrators.utils.orchestrator_monitoring import OrchestratorMonitor


class FuturesPairDataOrchestrator:
    """
    Orchestrates the backfill process for futures market data across multiple exchanges and trading pairs.
    
    This class manages futures-specific data writers, provides checkpointing and recovery
    capabilities, and supports parallel processing where appropriate.
    """
    
    # Define futures writer types
    WRITER_TYPES = [
        "funding_data_writer",
        "liquidations_data_writer",
        "open_interest_data_writer",
        "long_short_data_writer"
    ]
    
    # Futures writers are mostly independent, so no complex dependencies
    WRITER_DEPENDENCIES = {
        "funding_data_writer": [],
        "liquidations_data_writer": [],
        "open_interest_data_writer": [],
        "long_short_data_writer": []
    }
    
    def __init__(self, config: Dict, common_utils: CommonUtils):
        """
        Initialize the orchestrator with configuration and utilities.

        Args:
            config: Configuration dictionary for the orchestrator
            common_utils: CommonUtils instance for logging and other utilities
        """
        self.config = config
        self.common_utils = common_utils
        self.logger = common_utils.log

        # Initialize monitoring and enhanced logging
        self.monitor = OrchestratorMonitor(common_utils, "FuturesPairDataOrchestrator")
        
        # Set up checkpoint directory
        self.checkpoint_dir = os.path.join(
            os.path.dirname(os.path.abspath(__file__)), 
            "..", "..", "..", "data", "checkpoints", "futures"
        )
        os.makedirs(self.checkpoint_dir, exist_ok=True)
        
        # Validate configuration
        self._validate_config()
        
        # Initialize writer modules
        self.writer_modules = {}
        
    def _validate_config(self) -> None:
        """
        Validate the configuration dictionary.
        
        Raises:
            ValueError: If the configuration is invalid
        """
        required_keys = ["start_date", "end_date", "pairs"]
        for key in required_keys:
            if key not in self.config:
                raise ValueError(f"Missing required configuration key: {key}")
        
        # Validate date formats
        for date_key in ["start_date", "end_date"]:
            try:
                datetime.strptime(self.config[date_key], "%Y-%m-%d-%H:%M:%S")
            except ValueError:
                raise ValueError(f"Invalid date format for {date_key}: {self.config[date_key]}, expected YYYY-MM-DD-HH:MM:SS")
        
        # Validate pairs configuration
        if not isinstance(self.config["pairs"], dict):
            raise ValueError("Pairs configuration must be a dictionary mapping exchanges to lists of pairs")
        
        for exchange, pairs in self.config["pairs"].items():
            if not isinstance(pairs, list):
                raise ValueError(f"Pairs for exchange {exchange} must be a list")
            
            # Validate futures pair format (typically uppercase for futures)
            for pair in pairs:
                if not pair.isupper():
                    self.logger.warning(f"Futures pair {pair} for exchange {exchange} is not uppercase, which is unusual for futures")
    
    def _generate_checkpoint_key(self, exchange: str, pair: str, writer_type: str) -> str:
        """
        Generate a unique key for a checkpoint.
        
        Args:
            exchange: Exchange name
            pair: Trading pair
            writer_type: Writer type
            
        Returns:
            Unique checkpoint key
        """
        return f"{exchange}_{pair}_{writer_type}"
    
    def _generate_config_hash(self) -> str:
        """
        Generate a hash of the configuration for checkpoint validation.
        
        Returns:
            Hash string of the configuration
        """
        config_str = json.dumps(self.config, sort_keys=True)
        return hashlib.md5(config_str.encode()).hexdigest()
    
    def _get_checkpoint_path(self, exchange: str, pair: str, writer_type: str) -> str:
        """
        Get the path to a checkpoint file.
        
        Args:
            exchange: Exchange name
            pair: Trading pair
            writer_type: Writer type
            
        Returns:
            Path to checkpoint file
        """
        key = self._generate_checkpoint_key(exchange, pair, writer_type)
        
        # Check if we should use JSON or CSV
        if self.config.get("checkpoint_format", "json") == "csv":
            return os.path.join(self.checkpoint_dir, f"{key}.csv")
        else:
            return os.path.join(self.checkpoint_dir, f"{key}.json")
    
    def save_checkpoint(self, exchange: str, pair: str, checkpoint_key: str, end_date: str) -> None:
        """
        Save checkpoint for a specific exchange, pair, and writer.
        
        Args:
            exchange: Exchange name
            pair: Trading pair
            checkpoint_key: Checkpoint key
            end_date: End date to save as checkpoint
        """
        # Create checkpoint directory if it doesn't exist
        os.makedirs(self.checkpoint_dir, exist_ok=True)
        
        # Generate checkpoint file path
        checkpoint_file = os.path.join(self.checkpoint_dir, f"{checkpoint_key}.json")
        
        # Create checkpoint data
        checkpoint = {
            "end_date": end_date,
            "timestamp": datetime.now(pytz.UTC).strftime("%Y-%m-%d-%H:%M:%S"),
            "config_hash": self._generate_config_hash()
        }
        
        # Save checkpoint
        with open(checkpoint_file, "w") as f:
            json.dump(checkpoint, f, indent=4)
        
        self.logger.info(f"Saved checkpoint for {exchange} {pair} {checkpoint_key} at {end_date}")
    
    def load_checkpoint(self, exchange: str, pair: str, checkpoint_key: str) -> Dict:
        """
        Load checkpoint for a specific exchange, pair, and writer.
        
        Args:
            exchange: Exchange name
            pair: Trading pair
            checkpoint_key: Checkpoint key
            
        Returns:
            Dictionary with checkpoint data if found, None otherwise
        """
        self.logger.info(f"Loading checkpoint for {exchange} {pair} {checkpoint_key}")
        
        # Check if checkpoint directory exists
        if not os.path.exists(self.checkpoint_dir):
            return None
        
        # Generate checkpoint file path
        checkpoint_file = os.path.join(self.checkpoint_dir, f"{checkpoint_key}.json")
        
        # Check if checkpoint file exists
        if not os.path.exists(checkpoint_file):
            # Try legacy checkpoint format
            legacy_checkpoint_file = os.path.join(self.checkpoint_dir, f"{exchange}_{pair}_{checkpoint_key.split('_')[-1]}.json")
            if os.path.exists(legacy_checkpoint_file):
                checkpoint_file = legacy_checkpoint_file
            else:
                return None
        
        try:
            with open(checkpoint_file, "r") as f:
                checkpoint = json.load(f)
                return checkpoint
        except Exception as e:
            self.logger.error(f"Error loading checkpoint: {str(e)}")
            return None
    
    def handle_exchange_futures_specifics(self, exchange: str, pair: str) -> Dict:
        """
        Handle exchange-specific formatting for futures pairs.
        
        Args:
            exchange: Exchange name
            pair: Trading pair
            
        Returns:
            Dictionary with formatted pair and contract type
        """
        result = {
            "pair": pair,
            "contract_type": "perpetual"
        }
        
        # Binance uppercase formatting
        if exchange == "binance" and pair == pair.lower():
            self.logger.warning(f"Futures pair {pair} for exchange {exchange} is not uppercase, which is unusual for futures")
            result["pair"] = pair.upper()
        
        # BitMEX and Bybit contract types
        if exchange in ["bitmex", "bybit"]:
            if pair.endswith("USD"):
                result["contract_type"] = "inverse_perpetual"
            elif pair.endswith("USDT"):
                result["contract_type"] = "linear_perpetual"
            elif re.match(r".*\d{2}$", pair):  # e.g. BTCM22
                result["contract_type"] = "dated"
        
        return result
    
    def run_futures_writer(self, writer_type: str, exchange: str, pair: str, start_date: str, end_date: str) -> bool:
        """
        Run a futures writer with the given parameters.
        
        Args:
            writer_type: Writer type (funding_data_writer, liquidations_data_writer, etc.)
            exchange: Exchange name
            pair: Trading pair
            start_date: Start date in format YYYY-MM-DD-HH:MM:SS
            end_date: End date in format YYYY-MM-DD-HH:MM:SS
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Handle exchange-specific formatting
            specifics = self.handle_exchange_futures_specifics(exchange, pair)
            formatted_pair = specifics["pair"]

            # Use enhanced monitoring for execution tracking
            with self.monitor.track_execution_time(writer_type, exchange, formatted_pair) as metrics:
                # Import writer module
                writer_module = importlib.import_module(f"framework.data.writers.{writer_type}")

                # Report progress start
                self.monitor.report_progress(exchange, formatted_pair, writer_type, 0.0)

                # Generate checkpoint key
                checkpoint_key = f"{exchange}_{formatted_pair}_{writer_type}"

                # Load checkpoint if exists
                checkpoint = self.load_checkpoint(exchange, formatted_pair, checkpoint_key)
                if checkpoint and "end_date" in checkpoint:
                    self.logger.info(f"Resuming {writer_type} for {exchange} {formatted_pair} from {checkpoint['end_date']} to {end_date}")
                    start_date = checkpoint["end_date"]

                # Prepare writer arguments
                writer_args = [
                    "-s", start_date,
                    "-e", end_date,
                    "-i", formatted_pair,
                    "-x", exchange
                ]

                # Add parallel flag if enabled
                if self.config.get("parallel", False):
                    writer_args.append("-p")

                # Report progress mid-way
                self.monitor.report_progress(exchange, formatted_pair, writer_type, 0.5)

                # Run writer
                result = writer_module.main(writer_args)

                # Report progress completion
                self.monitor.report_progress(exchange, formatted_pair, writer_type, 1.0)

                # Save checkpoint if successful
                if result:
                    self.save_checkpoint(exchange, formatted_pair, checkpoint_key, end_date)

                return result
        except ImportError as e:
            # Enhanced error handling
            can_continue = self.monitor.handle_writer_error(e, writer_type, exchange, pair)
            return False
        except Exception as e:
            # Enhanced error handling
            can_continue = self.monitor.handle_writer_error(e, writer_type, exchange, pair)
            return False

    def run_funding_data_writer(self, exchange: str, pair: str, start_date: str, end_date: str) -> bool:
        """
        Execute the funding data writer for a specific exchange and pair.
        
        Args:
            exchange: Exchange name (e.g., 'binance')
            pair: Trading pair (e.g., 'BTCUSDT')
            start_date: Start date in format 'YYYY-MM-DD-HH:MM:SS'
            end_date: End date in format 'YYYY-MM-DD-HH:MM:SS'
            
        Returns:
            True if successful, False otherwise
        """
        return self.run_futures_writer("funding_data_writer", exchange, pair, start_date, end_date)

    def run_liquidations_data_writer(self, exchange: str, pair: str, start_date: str, end_date: str) -> bool:
        """
        Execute the liquidations data writer for a specific exchange and pair.
        
        Args:
            exchange: Exchange name (e.g., 'binance')
            pair: Trading pair (e.g., 'BTCUSDT')
            start_date: Start date in format 'YYYY-MM-DD-HH:MM:SS'
            end_date: End date in format 'YYYY-MM-DD-HH:MM:SS'
            
        Returns:
            True if successful, False otherwise
        """
        return self.run_futures_writer("liquidations_data_writer", exchange, pair, start_date, end_date)

    def run_open_interest_data_writer(self, exchange: str, pair: str, start_date: str, end_date: str) -> bool:
        """
        Execute the open interest data writer for a specific exchange and pair.
        
        Args:
            exchange: Exchange name (e.g., 'binance')
            pair: Trading pair (e.g., 'BTCUSDT')
            start_date: Start date in format 'YYYY-MM-DD-HH:MM:SS'
            end_date: End date in format 'YYYY-MM-DD-HH:MM:SS'
            
        Returns:
            True if successful, False otherwise
        """
        return self.run_futures_writer("open_interest_data_writer", exchange, pair, start_date, end_date)

    def run_long_short_data_writer(self, exchange: str, pair: str, start_date: str, end_date: str) -> bool:
        """
        Execute the long/short data writer for a specific exchange and pair.
        
        Args:
            exchange: Exchange name (e.g., 'binance')
            pair: Trading pair (e.g., 'BTCUSDT')
            start_date: Start date in format 'YYYY-MM-DD-HH:MM:SS'
            end_date: End date in format 'YYYY-MM-DD-HH:MM:SS'
            
        Returns:
            True if successful, False otherwise
        """
        return self.run_futures_writer("long_short_data_writer", exchange, pair, start_date, end_date)
    
    def run_writer(self, writer_type: str, exchange: str, pair: str, start_date: str, end_date: str) -> bool:
        """
        Run a futures writer for a specific exchange and pair.
        
        Args:
            writer_type: Writer type
            exchange: Exchange name
            pair: Trading pair
            start_date: Start date
            end_date: End date
            
        Returns:
            True if successful, False otherwise
        """
        return self.run_futures_writer(writer_type, exchange, pair, start_date, end_date)
    
    def run_writers_in_parallel(self, writer_types: List[str], exchange: str, pairs: List[str], 
                              start_date: str, end_date: str) -> Dict[str, Dict[str, bool]]:
        """
        Run multiple writers in parallel for multiple pairs.
        
        Args:
            writer_types: List of writer types to run
            exchange: Exchange name
            pairs: List of trading pairs
            start_date: Start date in format YYYY-MM-DD-HH:MM:SS
            end_date: End date in format YYYY-MM-DD-HH:MM:SS
            
        Returns:
            Dictionary of results by writer type and pair
        """
        self.logger.info(f"Running all writers in parallel for {exchange} with {len(pairs)} pairs from {start_date} to {end_date}")
        
        results = {}
        
        # Run each writer type sequentially
        for writer_type in writer_types:
            # Initialize results for this writer type
            results[writer_type] = {}
            
            # Run all pairs in parallel for this writer type
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.config.get("max_workers", 10)) as executor:
                # Submit tasks for each pair
                future_to_pair = {
                    executor.submit(
                        self.run_futures_writer, 
                        writer_type, 
                        exchange, 
                        pair, 
                        start_date, 
                        end_date
                    ): pair for pair in pairs
                }
                
                # Process results as they complete
                for future in concurrent.futures.as_completed(future_to_pair):
                    pair = future_to_pair[future]
                    try:
                        result = future.result()
                        results[writer_type][pair] = result
                    except Exception as e:
                        self.logger.error(f"Error running {writer_type} for {exchange} {pair}: {str(e)}")
                        results[writer_type][pair] = False
        
        return results
    
    def run_all(self) -> Dict:
        """
        Run all writers for all exchanges and pairs.

        Returns:
            Dictionary with results for each exchange, pair, and writer
        """
        # Start global execution tracking
        self.monitor.start_global_execution()

        self.logger.info("Running all writers for all exchanges and pairs")

        # Track overall execution time
        overall_start_time = datetime.now()

        results = {}
        
        # Process each exchange
        for exchange, pairs in self.config["pairs"].items():
            self.logger.info(f"Running all writers for {exchange} with {len(pairs)} pairs")
            
            # Track exchange execution time
            exchange_start_time = datetime.now()
            
            if self.config.get("parallel", False):
                # Run all writers in parallel for this exchange
                exchange_results = self.run_writers_in_parallel(
                    self.WRITER_TYPES, 
                    exchange, 
                    pairs, 
                    self.config["start_date"], 
                    self.config["end_date"]
                )
            else:
                # Run all writers sequentially for this exchange
                exchange_results = {}
                for pair in pairs:
                    pair_results = {}
                    for writer_type in self.WRITER_TYPES:
                        self.logger.info(f"Running {writer_type} for {exchange} {pair}")
                        result = self.run_writer(
                            writer_type, 
                            exchange, 
                            pair, 
                            self.config["start_date"], 
                            self.config["end_date"]
                        )
                        pair_results[writer_type] = result
                    
                    # Store results for this pair
                    if exchange not in exchange_results:
                        exchange_results[exchange] = {}
                    exchange_results[exchange][pair] = pair_results
            
            # Store results for this exchange
            results[exchange] = exchange_results
            
            # Log exchange execution time
            exchange_end_time = datetime.now()
            exchange_execution_time = (exchange_end_time - exchange_start_time).total_seconds()
            self.logger.info(f"Completed all writers for {exchange} in {exchange_execution_time:.2f} seconds")
        
        # Log overall execution time
        overall_end_time = datetime.now()
        overall_execution_time = (overall_end_time - overall_start_time).total_seconds()
        self.logger.info(f"Completed all writers for all exchanges and pairs in {overall_execution_time:.2f} seconds")

        # End global execution tracking and generate summary
        self.monitor.end_global_execution()
        summary = self.monitor.generate_execution_summary()

        # Add summary to results
        results["execution_summary"] = summary

        return results


def main():
    """
    Main function for running the futures pair data orchestrator.
    """
    import argparse
    
    parser = argparse.ArgumentParser(description="Futures Pair Data Orchestrator")
    parser.add_argument("-c", "--config", required=True, help="Path to configuration file")
    parser.add_argument("-w", "--writer", help="Specific writer to run")
    parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    # Set up logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    common_utils = CommonUtils(log_level=log_level)
    logger = common_utils.log
    
    # Load configuration
    try:
        with open(args.config, 'r') as f:
            config = json.load(f)
    except Exception as e:
        logger.error(f"Error loading configuration: {str(e)}")
        sys.exit(1)
    
    # Create orchestrator
    orchestrator = FuturesPairDataOrchestrator(config, common_utils)
    
    # Run orchestrator
    try:
        if args.writer:
            # Run specific writer with execution tracking
            orchestrator.monitor.start_global_execution()
            results = {}
            for exchange, pairs in config["pairs"].items():
                exchange_results = {}
                for pair in pairs:
                    result = orchestrator.run_writer(args.writer, exchange, pair, config["start_date"], config["end_date"])
                    exchange_results[pair] = result
                results[exchange] = exchange_results
            orchestrator.monitor.end_global_execution()
            summary = orchestrator.monitor.generate_execution_summary()
            results["execution_summary"] = summary
        else:
            # Run all writers
            results = orchestrator.run_all()

        logger.info("Orchestrator run complete")

        # Log results without the execution summary for cleaner output
        results_without_summary = {k: v for k, v in results.items() if k != "execution_summary"}
        logger.info(f"Results: {json.dumps(results_without_summary, indent=2)}")

        # The execution summary is already logged by the monitor

    except Exception as e:
        logger.error(f"Error running orchestrator: {str(e)}")
        # Try to generate a final summary even on error
        try:
            orchestrator.monitor.end_global_execution()
            orchestrator.monitor.generate_execution_summary()
        except:
            pass
        sys.exit(1)


if __name__ == "__main__":
    main()
