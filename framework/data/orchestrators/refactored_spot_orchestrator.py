#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Refactored Spot Pair Data Orchestrator

This module provides a refactored version of the spot pair data orchestrator
using the new utility modules for better code organization and reduced duplication.
"""

import argparse
import json
import sys
from typing import Dict, List, Any, Optional

from framework.utils.common import CommonUtils
from .utils.base_orchestrator import BaseOrchestrator
from .utils.writer_executor import WriterExecutionConfig


class RefactoredSpotPairDataOrchestrator(BaseOrchestrator):
    """
    Refactored orchestrator for spot market data backfill using utility modules.
    
    This class manages dependencies between different data types, provides checkpointing
    and recovery capabilities, and supports parallel processing using the new utility framework.
    """
    
    # Define writer levels and dependencies
    WRITER_LEVELS = {
        "level_1": ["tick_data_writer"],
        "level_2": ["price_derived_data_writer", "volume_data_writer", "orderbook_data_writer"],
        "level_3": ["volatility_data_writer"],
        "level_4": ["correlation_data_writer"],
        "level_5": ["price_market_index_data_writer"]
    }
    
    # Define dependencies between writers
    WRITER_DEPENDENCIES = {
        "tick_data_writer": [],
        "price_derived_data_writer": ["tick_data_writer"],
        "volume_data_writer": ["tick_data_writer"],
        "orderbook_data_writer": ["tick_data_writer"],
        "volatility_data_writer": ["price_derived_data_writer"],
        "correlation_data_writer": ["price_derived_data_writer", "volume_data_writer"],
        "price_market_index_data_writer": ["price_derived_data_writer", "volume_data_writer", "volatility_data_writer"]
    }
    
    def __init__(self, config: Dict[str, Any], common_utils: CommonUtils):
        """
        Initialize the refactored spot orchestrator.
        
        Args:
            config: Configuration dictionary
            common_utils: CommonUtils instance for logging and utilities
        """
        super().__init__(config, common_utils, "spot")
        
        # Validate writer dependencies
        self._validate_writer_dependencies()
    
    def _validate_writer_dependencies(self) -> None:
        """Validate that all writer dependencies are properly defined."""
        all_writers = set()
        for level_writers in self.WRITER_LEVELS.values():
            all_writers.update(level_writers)
        
        for writer in all_writers:
            if writer not in self.WRITER_DEPENDENCIES:
                raise ValueError(f"Writer {writer} is missing dependency definition")
        
        self.logger.info(f"Validated dependencies for {len(all_writers)} writers across {len(self.WRITER_LEVELS)} levels")
    
    def get_writer_types(self) -> List[str]:
        """Get list of all writer types supported by this orchestrator."""
        all_writers = []
        for level_writers in self.WRITER_LEVELS.values():
            all_writers.extend(level_writers)
        return all_writers
    
    def _get_exchange_formatter(self) -> Optional[callable]:
        """Get exchange-specific formatter for spot markets."""
        def spot_formatter(exchange: str, pair: str) -> Dict[str, Any]:
            format_result = self.exchange_handler.format_spot_pair(exchange, pair)
            return {"pair": format_result.pair}
        
        return spot_formatter
    
    def check_dependencies(self, writer_type: str, exchange: str, pair: str) -> bool:
        """
        Check if dependencies for a writer are satisfied.
        
        Args:
            writer_type: Writer type to check
            exchange: Exchange name
            pair: Trading pair
            
        Returns:
            True if all dependencies are satisfied, False otherwise
        """
        dependencies = self.WRITER_DEPENDENCIES.get(writer_type, [])
        
        for dependency in dependencies:
            checkpoint = self.checkpoint_manager.load_checkpoint(exchange, pair, dependency)
            if not checkpoint:
                self.logger.warning(f"Dependency {dependency} not satisfied for {writer_type} on {exchange} {pair}")
                return False
        
        return True
    
    def run_correlation_writer(self, exchange: str, pair: str, start_date: str, 
                              end_date: str, timeframe: str) -> bool:
        """
        Run correlation writer for a specific timeframe.
        
        Args:
            exchange: Exchange name
            pair: Trading pair
            start_date: Start date for execution
            end_date: End date for execution
            timeframe: Timeframe for correlation analysis
            
        Returns:
            True if execution was successful, False otherwise
        """
        return self.writer_executor.execute_correlation_writer(
            WriterExecutionConfig(
                writer_type="correlation_data_writer",
                exchange=exchange,
                pair=pair,
                start_date=start_date,
                end_date=end_date,
                parallel=self.config.get("parallel", False)
            ),
            timeframe
        )
    
    def run_level(self, level: str) -> Dict[str, Any]:
        """
        Run all writers for a specific level.
        
        Args:
            level: Level to run (e.g., "level_1", "level_2")
            
        Returns:
            Dictionary with results for each writer in the level
        """
        if level not in self.WRITER_LEVELS:
            raise ValueError(f"Unknown level: {level}")
        
        self.logger.info(f"Running {level} with writers: {self.WRITER_LEVELS[level]}")
        
        results = {}
        
        for writer_type in self.WRITER_LEVELS[level]:
            self.logger.info(f"Running {writer_type}")
            writer_results = {}
            
            # Handle correlation writer specially
            if writer_type == "correlation_data_writer":
                for exchange, pairs in self.config["pairs"].items():
                    exchange_results = {}
                    
                    if self.config.get("parallel", False):
                        # Run correlation analysis in parallel for all pairs and timeframes
                        timeframes = ["1h", "12h", "1d"]
                        
                        def correlation_function(exchange: str, pair: str, start_date: str, end_date: str, timeframe: str) -> bool:
                            return self.run_correlation_writer(exchange, pair, start_date, end_date, timeframe)
                        
                        correlation_results = self.parallel_executor.execute_correlation_parallel(
                            pairs, exchange, timeframes, self.config["start_date"], self.config["end_date"], correlation_function
                        )
                        exchange_results = correlation_results
                    else:
                        # Run correlation analysis sequentially
                        for pair in pairs:
                            pair_results = {}
                            for timeframe in ["1h", "12h", "1d"]:
                                result = self.run_correlation_writer(
                                    exchange, pair, self.config["start_date"], self.config["end_date"], timeframe
                                )
                                pair_results[timeframe] = result
                            exchange_results[pair] = pair_results
                    
                    writer_results[exchange] = exchange_results
            else:
                # Handle regular writers
                for exchange, pairs in self.config["pairs"].items():
                    if self.config.get("parallel", False):
                        # Run writers in parallel for this exchange
                        exchange_results = self.execute_writers_parallel(
                            [writer_type], exchange, pairs, self.config["start_date"], self.config["end_date"]
                        )
                        writer_results[exchange] = exchange_results.get(writer_type, {})
                    else:
                        # Run writers sequentially
                        exchange_results = {}
                        for pair in pairs:
                            # Check dependencies
                            if not self.check_dependencies(writer_type, exchange, pair):
                                self.logger.error(f"Dependencies not satisfied for {writer_type} on {exchange} {pair}")
                                exchange_results[pair] = False
                                continue
                            
                            result = self.execute_writer_for_pair(
                                writer_type, exchange, pair, self.config["start_date"], self.config["end_date"]
                            )
                            exchange_results[pair] = result
                        
                        writer_results[exchange] = exchange_results
            
            results[writer_type] = writer_results
        
        return results
    
    def run_all_operations(self) -> Dict[str, Any]:
        """
        Run all writers for all levels.
        
        Returns:
            Dictionary with results for each level
        """
        self.logger.info("Running all levels")
        
        results = {}
        
        for level in self.WRITER_LEVELS.keys():
            level_results = self.run_level(level)
            results[level] = level_results
        
        return results
    
    def run_writer(self, writer_type: str, exchange: str, pair: str, 
                   start_date: str, end_date: str) -> bool:
        """
        Run a specific writer for a specific exchange and pair.
        
        Args:
            writer_type: Writer type to run
            exchange: Exchange name
            pair: Trading pair
            start_date: Start date for execution
            end_date: End date for execution
            
        Returns:
            True if execution was successful, False otherwise
        """
        # Check dependencies
        if not self.check_dependencies(writer_type, exchange, pair):
            self.logger.error(f"Dependencies not satisfied for {writer_type} on {exchange} {pair}")
            return False
        
        return self.execute_writer_for_pair(writer_type, exchange, pair, start_date, end_date)


def main(argv: List[str] = None) -> None:
    """
    Main function for the refactored spot orchestrator.
    
    Args:
        argv: Command line arguments
    """
    parser = argparse.ArgumentParser(description="Refactored Spot Pair Data Orchestrator")
    parser.add_argument("-c", "--config", required=True, help="Configuration file path")
    parser.add_argument("-l", "--level", help="Specific level to run")
    parser.add_argument("-w", "--writer", help="Specific writer to run")
    parser.add_argument("-v", "--verbose", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args(argv)
    
    # Initialize CommonUtils
    log_level = "DEBUG" if args.verbose else "INFO"
    common_utils = CommonUtils(name="refactored_spot_orchestrator", log_level=log_level)
    logger = common_utils.log
    
    try:
        # Load configuration
        config_manager = ConfigManager(common_utils)
        config = config_manager.load_config_from_file(args.config)
        
        # Initialize orchestrator
        orchestrator = RefactoredSpotPairDataOrchestrator(config, common_utils)
        
        # Run orchestrator
        if args.level:
            # Run specific level with monitoring
            orchestrator.monitor.start_global_execution()
            results = orchestrator.run_level(args.level)
            orchestrator.monitor.end_global_execution()
            summary = orchestrator.monitor.generate_execution_summary()
            results["execution_summary"] = summary
        elif args.writer:
            # Run specific writer with monitoring
            orchestrator.monitor.start_global_execution()
            results = {}
            for exchange, pairs in config["pairs"].items():
                exchange_results = {}
                for pair in pairs:
                    result = orchestrator.run_writer(args.writer, exchange, pair, config["start_date"], config["end_date"])
                    exchange_results[pair] = result
                results[exchange] = exchange_results
            orchestrator.monitor.end_global_execution()
            summary = orchestrator.monitor.generate_execution_summary()
            results["execution_summary"] = summary
        else:
            # Run all operations with monitoring
            results = orchestrator.run_all_with_monitoring()
        
        logger.info("Orchestrator run complete")
        
        # Log results without the execution summary for cleaner output
        results_without_summary = {k: v for k, v in results.items() if k != "execution_summary"}
        logger.info(f"Results: {json.dumps(results_without_summary, indent=2)}")
        
    except Exception as e:
        logger.error(f"Error running orchestrator: {str(e)}")
        sys.exit(1)
    finally:
        common_utils.finalize()


if __name__ == "__main__":
    main()
